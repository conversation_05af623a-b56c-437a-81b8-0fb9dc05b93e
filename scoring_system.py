"""
P<PERSON>lama sistemi dokümantasyonu scoring_documentation.md dosyasına taşınmıştır.
"""

import os
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
import pandas as pd
from loguru import logger
from utils import format_price_standard
from stats_tracker import StatsTracker
from premium_discount_analyzer import PremiumDiscountAnalyzer
from bybit_client import BybitClient

# Logger formatını değiştir - Modül/fonksiyon/satır bilgisini gizle
logger = logger.bind(format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")

# Stratejileri izole etmek için yardımcı sınıfları tanımla
class ScoringSystem:
    """
    Farklı teknik analiz sonuçlarını birleştiren ve puanlama yapan sınıf.
    Confluence (Kesişim) stratejisine dayanarak, işlem fırsatlarını puanlar
    ve sıralar.

    VERİ ERİŞİM NOTU: Bu sınıf, `calculate_score` metoduna geçirilen parametreler
    aracılığıyla o anki zaman dilimine ait verileri alır. Diğer zaman dilimlerine
    ait verilerin (örneğin `_calculate_other_timeframe_scores` içinde kullanılacaksa)
    `calculate_score` metoduna farklı bir yapıda (örn. tüm zaman dilimlerini içeren
    tek bir sözlük) iletilmesi veya `ScoringSystem` örneğine başka bir yöntemle
    (örn. ayrı bir `set_all_data` metodu) sağlanması gerekir. Mevcut haliyle
    `_calculate_other_timeframe_scores` metodu diğer zaman dilimleri için
    gerçek veri işleyemez.
    """

    def __init__(self, stats_tracker: Optional[StatsTracker] = None):
        """
        ScoringSystem sınıfını başlatır.

        Args:
            stats_tracker: İşlem istatistiklerini takip eden StatsTracker örneği
        """
        self.scores: Dict[str, Dict[str, Any]] = {}
        self.main_timeframe: str = "240"
        self.other_timeframes: List[str] = ["D", "720", "60"]
        self.stats_tracker = stats_tracker
        self.pd_analyzer = PremiumDiscountAnalyzer()  # Premium/Discount analiz modülünü başlat

        # Akıllı giriş stratejisi sınıfını başlat
        from smart_entry_strategy import SmartEntryStrategy
        self.smart_entry_strategy = SmartEntryStrategy()

        logger.info("Puanlama sistemi başlatıldı")

    def reset_scores(self):
        """
        Artık her döngüde puanları sıfırlamıyoruz.
        Puanlar, pattern koşulu değişene kadar korunacak.
        Bu fonksiyon, geriye dönük uyumluluk için korunmuştur.
        """
        # Puanları sıfırlamıyoruz, sadece log mesajı yazıyoruz
        logger.debug("Puanlama sistemi sıfırlama fonksiyonu çağrıldı, ancak puanlar korunuyor.")

    def format_price(self, price):
        """Fiyatı formatlar"""
        if price is None:
            return "None"
        try:
            return format_price_standard(price)
        except (TypeError, ValueError):
            logger.warning(f"Fiyat formatlanamadı: {price}")
            return str(price)

    # Fonksiyon tanımına 'candles' parametresini ekleyin
    def calculate_score(self, symbol: str, timeframe: str,
                        patterns: Optional[List[Dict[str, str]]] = None,
                        divergences: Optional[List[Dict[str, Any]]] = None,
                        stats: Optional[Dict[str, Any]] = None,
                        bos_results: Optional[Dict[str, Any]] = None,
                        daily_bias: Optional[int] = None,
                        fib_data: Optional[Dict[str, Any]] = None,
                        fib_data_4h: Optional[Dict[str, Any]] = None,
                        fvg_data: Optional[List[Dict[str, Any]]] = None,
                        order_blocks: Optional[Dict[str, Dict[str, Any]]] = None,
                        all_timeframe_data: Optional[Dict[str, Dict[str, Dict[str, Any]]]] = None,
                        daily_stats: Optional[Dict[str, Any]] = None,
                        candles: Optional[pd.DataFrame] = None) -> Dict[str, Any]: # <-- YENİ PARAMETRE
        """
        Verilen teknik analiz sonuçlarına göre sembol puanını hesaplar.

        Args:
            symbol (str): Kripto para sembolü
            timeframe (str): Zaman dilimi
            patterns (List[Dict]): Tespit edilen örüntüler listesi
            divergences (List[Dict]): Tespit edilen uyumsuzluklar listesi
            stats (Dict): Fiyat ve hacim istatistikleri
            bos_results (Dict): Break of Structure analiz sonuçları
            daily_bias (Dict): Günlük yön analizi sonuçları
            fib_data (Dict): Fibonacci analiz sonuçları
            fvg_data (Dict): FVG (Fair Value Gap) analiz sonuçları
            order_blocks (Dict): Sipariş blokları bilgileri
            all_timeframe_data (Dict): Tüm zaman dilimleri için veriler (Günlük swing/pivot noktaları buradan alınır)
            daily_stats (Dict): Günlük zaman dilimi için stats verisi

        Returns:
            Dict: Hesaplanan puanlamalar ve ayrıntılar
        """
        scores_breakdown = []
        total_score = 0.0

        # Stats yoksa puanlama yapılamaz
        if not stats:
            logger.warning(f"[{symbol} {timeframe}] Fiyat istatistikleri bulunamadı, puanlama yapılamıyor.")
            return {
                "total_score": 0.0,
                "scores": [],
                "errors": ["Fiyat istatistikleri bulunamadı, puanlama yapılamıyor."]
            }

        # --- ICT Premium/Discount Hesaplama ---
        # Ana zaman dilimi için, günlük pivotlara dayalı Premium/Discount bölgelerini hesapla
        pd_zones = None
        if timeframe == self.main_timeframe:
            # Günlük (D) zaman dilimindeki swing_points'i al
            daily_swing_points = None
            if all_timeframe_data and symbol in all_timeframe_data and 'D' in all_timeframe_data[symbol]:
                daily_swing_points = all_timeframe_data[symbol]['D'].get('swing_points')
                if daily_swing_points:
                    logger.debug(f"[{symbol}] Günlük (D) swing points bulundu, Premium/Discount hesaplaması için kullanılacak.")
                else:
                    logger.warning(f"[{symbol}] Günlük (D) swing points bulunamadı, Premium/Discount hesaplaması yapılamayabilir.")
            else:
                logger.warning(f"[{symbol}] Günlük (D) zaman dilimi verisi bulunamadı, Premium/Discount hesaplaması yapılamayabilir.")

            # Günlük swing_points'i kullanarak Premium/Discount hesapla
            pd_zones = self.pd_analyzer.calculate_daily_premium_discount(symbol, daily_stats, daily_swing_points)

            # Pivot bilgilerini loga ekle
            # Hata durumu ('status': 'error') dönmediği sürece pivotları logla
            if pd_zones and pd_zones.get('status') != 'error':
                high = pd_zones.get('high')
                low = pd_zones.get('low')
                eq = pd_zones.get('eq_level')
                last_pivot_type = pd_zones.get('last_pivot_type', 'N/A')
                prev_pivot_type = pd_zones.get('prev_pivot_type', 'N/A')
                logger.debug(f"[{symbol}] Pivot: High={high:.2f}, Low={low:.2f}, EQ={eq:.2f}, Pivotlar: {prev_pivot_type}->{last_pivot_type} (Premium/Discount hesaplamasında kullanılan değerler)")
            else:
                logger.debug(f"[{symbol}] Premium/Discount bölgeleri hesaplandı: {pd_zones}")

        # --- Yön Belirleme ve Puanlama Akışı ---
        main_trade_direction: Optional[str] = None # Nihai yönü saklayacak değişken

        try:
            # 1. Önce Pattern Puanlaması (Ana zaman dilimi için)
            # Henüz yön belirlenmediği için trade_direction=None olarak geçiyoruz
            base_pattern_score, base_pattern_details, pd_conflict_details = self._score_patterns(symbol, timeframe, patterns, bos_results, trade_direction=None)
            logger.debug(f"[{symbol} {timeframe}] Pattern puanlaması sonucu: Score={base_pattern_score}, Details Count={len(base_pattern_details)}, PD Conflicts: {len(pd_conflict_details)}")

            # Ana zaman diliminde pattern yoksa puanlama yapma
            if timeframe == self.main_timeframe and (not base_pattern_details or base_pattern_score == 0.0):
                logger.info(f"[{symbol} {timeframe}] Pattern tespit edilemedi, puanlama yapılmayacak.")
                return {
                    "total_score": 0.0,
                    "scores": [],
                    "errors": ["Pattern tespit edilemedi, puanlama yapılmayacak."]
                }

            # 2. Pattern'lere Göre Yönü Belirle (self.scores'a bakmadan)
            pattern_direction = self._determine_direction_from_patterns(base_pattern_details)
            logger.debug(f"[{symbol}] Ana işlem yönü (Pattern'lerden): {pattern_direction}")

            # Nihai yönü başlangıçta pattern yönü olarak ayarla
            main_trade_direction = pattern_direction

            # Eğer pattern yönü belirlenemezse (karışık pattern durumu), puanlama yapma
            if timeframe == self.main_timeframe and main_trade_direction is None:
                logger.info(f"[{symbol} {timeframe}] Pattern yönü belirlenemedi (karışık pattern), puanlama yapılmayacak.")
                return {
                    "total_score": 0.0,
                    "scores": [],
                    "errors": ["Pattern yönü belirlenemedi (karışık pattern), puanlama yapılmayacak."]
                }

            # --- 4. Skorları ve Nihai Yönü Kaydet ---
            # Ana zaman dilimi için skorları HER ZAMAN kaydet (base_score 0 olsa bile)
            if timeframe == self.main_timeframe:
                # Eğer sembol daha önce puanlanmışsa ve pattern koşulu değişmemişse, puanları yeniden hesaplama
                if symbol in self.scores:
                    old_base_details = self.scores[symbol].get("base_details", [])
                    old_direction = self.scores[symbol].get("direction")
                    # PD çakışmalarının da değişip değişmediğini kontrol etmemiz gerekebilir,
                    # ancak şimdilik sadece pattern ve yön değişikliğine odaklanalım.
                    # Eğer pd_conflict_details de değişerse, pattern_changed = True yapılabilir.

                    # Pattern koşulu değişmiş mi kontrol et
                    pattern_changed = False

                    # Pattern sayısı değiştiyse, pattern koşulu değişmiştir
                    if len(old_base_details) != len(base_pattern_details):
                        pattern_changed = True
                        logger.info(f"[{symbol}] Pattern sayısı değişti: {len(old_base_details)} -> {len(base_pattern_details)}")

                    # Pattern isimleri değiştiyse, pattern koşulu değişmiştir
                    if not pattern_changed:
                        old_pattern_names = [detail[1] for detail in old_base_details]
                        new_pattern_names = [detail[1] for detail in base_pattern_details]
                        if set(old_pattern_names) != set(new_pattern_names):
                            pattern_changed = True
                            logger.info(f"[{symbol}] Pattern isimleri değişti: {old_pattern_names} -> {new_pattern_names}")

                    # PD çakışmaları değiştiyse, pattern koşulu değişmiştir
                    if not pattern_changed:
                        old_pd_conflicts = self.scores[symbol].get("pd_conflict_details_for_check", []) # Kontrol için saklanan eski PD çakışmaları
                        # pd_conflict_details'i (açıklama, puan) formatından sadece açıklama listesine çevirerek karşılaştır
                        current_pd_conflict_names = [conflict[0] for conflict in pd_conflict_details]
                        if set(old_pd_conflicts) != set(current_pd_conflict_names):
                            pattern_changed = True
                            logger.info(f"[{symbol}] Premium/Discount çakışmaları değişti: {old_pd_conflicts} -> {current_pd_conflict_names}")


                    # Yön değiştiyse, pattern koşulu değişmiştir - Bu en önemli kontrol
                    if not pattern_changed and old_direction != main_trade_direction:
                        pattern_changed = True
                        logger.info(f"[{symbol}] Yön değişti: {old_direction} -> {main_trade_direction}")
                        # Yön değişikliği varsa, bu yeni bir işlem anlamına gelir
                        if old_direction is not None and main_trade_direction is not None:
                            logger.info(f"[{symbol}] Yön değişikliği tespit edildi: {old_direction} -> {main_trade_direction}, yeni işlem başlatılıyor.")

                    # Pattern koşulu değişmediyse, puanları yeniden hesaplama
                    if not pattern_changed:
                        logger.info(f"[{symbol}] Pattern koşulu değişmedi, puanlar korunuyor.")
                        return self.scores[symbol]
                    else:
                        logger.info(f"[{symbol}] Pattern koşulu değişti, puanlar yeniden hesaplanıyor.")

                # Sembol ilk kez puanlanıyorsa veya pattern koşulu değiştiyse, puanları hesapla
                if symbol not in self.scores: self.scores[symbol] = {}
                self.scores[symbol]["base_score"] = base_pattern_score # Pattern skoru (0 olabilir)
                self.scores[symbol]["base_details"] = base_pattern_details # Pattern detayları (boş olabilir)
                self.scores[symbol]["direction"] = main_trade_direction # Nihai belirlenen yön
                self.scores[symbol]["trade_direction"] = main_trade_direction # Report için aynı değer
                # Kontrol için PD çakışma açıklamalarını sakla
                self.scores[symbol]["pd_conflict_details_for_check"] = [conflict[0] for conflict in pd_conflict_details]


                # Premium/Discount bilgilerini kaydet (her zaman)
                # pd_zones artık her zaman bir değer döndürür (başarılı veya hata durumu)
                self.scores[symbol]["pd_zones"] = pd_zones

                # Durum kontrolü
                if pd_zones.get('status') == 'success':
                    # Başarılı hesaplama durumu
                    eq_label = f"EQ: {pd_zones['eq_level']:.4f}"
                    zone_label = "Premium" if pd_zones['is_premium'] else "Discount"
                    self.scores[symbol]["pd_label"] = f"{zone_label} ({eq_label})"

                    # Fibonacci seviyelerini skora ekle
                    if 'fib_levels' in pd_zones:
                        self.scores[symbol]["fib_levels"] = pd_zones['fib_levels']

                    # current_price değerini ekle (FVG/OB puanlaması için)
                    if 'current_price' not in pd_zones:
                        pd_zones['current_price'] = pd_zones.get('current_price', 0.0)
                else:
                    # Hata durumu
                    error_reason = pd_zones.get('reason', 'Bilinmeyen hata')
                    self.scores[symbol]["pd_label"] = f"Hesaplanamadı ({error_reason})"
                    zone_label = "Hesaplanamadı"
                    eq_label = "N/A"

                    # current_price değerini ekle (FVG/OB puanlaması için)
                    # Eğer all_timeframe_data içinde 240 zaman dilimi varsa, oradan son fiyatı al
                    if all_timeframe_data and symbol in all_timeframe_data and '240' in all_timeframe_data[symbol]:
                        tf_data = all_timeframe_data[symbol]['240']
                        if 'stats' in tf_data and tf_data['stats'] and 'last_price' in tf_data['stats']:
                            pd_zones['current_price'] = tf_data['stats']['last_price']
                        else:
                            pd_zones['current_price'] = 0.0
                    else:
                        pd_zones['current_price'] = 0.0

                # FVG ve OB bilgilerini de ekle
                fvg_info = ""
                ob_info = ""

                # FVG bilgilerini kontrol et
                if all_timeframe_data and symbol in all_timeframe_data and '240' in all_timeframe_data[symbol]:
                    tf_data = all_timeframe_data[symbol]['240']

                    # FVG bilgilerini ekle
                    if 'fvgs' in tf_data and tf_data['fvgs']:
                        fvgs = tf_data['fvgs']
                        bullish_fvgs = [fvg for fvg in fvgs if fvg['type'] == 'bullish']
                        bearish_fvgs = [fvg for fvg in fvgs if fvg['type'] == 'bearish']
                        fvg_info = f" | FVGs: {len(bullish_fvgs)} Bull, {len(bearish_fvgs)} Bear"

                    # OB bilgilerini ekle ve score_result'a kaydet
                    if 'order_blocks' in tf_data and tf_data['order_blocks']:
                        obs = tf_data['order_blocks']
                        bull_ob_exists = bool(obs.get('bullish'))
                        bear_ob_exists = bool(obs.get('bearish'))
                        
                        # Log için bilgi
                        bull_ob = "Yes" if bull_ob_exists else "No"
                        bear_ob = "Yes" if bear_ob_exists else "No"
                        ob_info = f" | OBs: Bull: {bull_ob}, Bear: {bear_ob}"
                        
                        # Score result'a OB bilgilerini kaydet (Adım A)
                        self.scores[symbol]["bull_ob"] = bull_ob_exists
                        self.scores[symbol]["bear_ob"] = bear_ob_exists
                    else:
                        # OB yoksa False olarak kaydet
                        self.scores[symbol]["bull_ob"] = False
                        self.scores[symbol]["bear_ob"] = False

                # Log mesajı
                if pd_zones.get('status') == 'success':
                    # Fibonacci seviyelerini log için hazırla
                    fib_info = ""
                    if 'fib_levels' in pd_zones:
                        # Sadece 0, EQ ve 1 seviyelerini göster
                        fib_info = f" | 0={pd_zones['fib_levels'].get('0', pd_zones.get('low', 0)):.4f}, 1={pd_zones['fib_levels'].get('1', pd_zones.get('high', 0)):.4f}"

                    logger.info(f"[{symbol}] Premium/Discount: {zone_label} bölgesi, {eq_label}{fib_info}{fvg_info}{ob_info}")
                else:
                    logger.info(f"[{symbol}] Premium/Discount: {self.scores[symbol]['pd_label']}{fvg_info}{ob_info}")

                # Başlangıç teyit/negatif skorlarını da burada ayarlayalım
                self.scores[symbol]["confirmation_score"] = 0.0
                self.scores[symbol]["confirmation_details"] = []
                self.scores[symbol]["negative_score"] = 0.0 # PD çakışmaları için başlangıç
                self.scores[symbol]["negative_details"] = [] # PD çakışmaları için başlangıç

                # PD Çakışmalarını işle
                if pd_conflict_details:
                    for conflict_reason, conflict_score in pd_conflict_details:
                        self.scores[symbol]["negative_score"] += conflict_score
                        self.scores[symbol]["negative_details"].append(f"🔴 {conflict_reason}") # Emoji eklendi
                        logger.info(f"[{symbol}] PD Çakışması eklendi: {conflict_reason}, Skor: {conflict_score}")

                logger.debug(f"[{symbol}] Ana skor kaydı oluşturuldu/güncellendi. Nihai Yön: {main_trade_direction}")

            # --- 5. Teyit ve Negatif Skorlar (Ana Zaman Dilimi) ---
            if timeframe == self.main_timeframe:
                # self.scores[symbol] artık kesin var ve 'direction' içeriyor
                logger.debug(f"[{symbol} {timeframe}] Ana zaman dilimi teyit/negatif skorları hesaplanıyor (Yön: {main_trade_direction})...")
                # _calculate_main_confirmation_scores zaten negative_score ve negative_details'i güncelliyor,
                # PD çakışmaları için olanlar yukarıda eklendi.
                self._calculate_main_confirmation_scores(
                    symbol=symbol,
                    timeframe=timeframe,
                    stats=stats,
                    daily_bias=daily_bias.get('bias') if isinstance(daily_bias, dict) else daily_bias,
                    supertrend=all_timeframe_data.get(symbol, {}).get(timeframe, {}).get("supertrend"),
                    fvgs=fvg_data,  # fvg_data parametresi kullanılıyor
                    fib_data=fib_data,
                    timeframe_levels=all_timeframe_data.get(symbol, {}).get(timeframe, {}).get("timeframe_levels"),
                    order_blocks=order_blocks,
                    naked_pocs=all_timeframe_data.get(symbol, {}).get(timeframe, {}).get("naked_pocs"),
                    sfps=all_timeframe_data.get(symbol, {}).get(timeframe, {}).get("sfps"),
                    divergences=divergences,
                    bos_results=bos_results,
                    trade_direction=main_trade_direction # Nihai yönü geçir
                )
                logger.debug(f"[{symbol} {timeframe}] Ana zaman dilimi teyit/negatif skorları tamamlandı.")

            # --- 6. Diğer Zaman Dilimlerinin Puanları (HTF Confluence) ---
            if all_timeframe_data and timeframe == self.main_timeframe:
                 # self.scores[symbol] artık kesin var
                 logger.debug(f"[{symbol}] Diğer zaman dilimi skorları hesaplanıyor (Ana Yön: {main_trade_direction})...")
                 try:
                    other_timeframe_scores = self._calculate_other_timeframe_scores(symbol, all_timeframe_data, main_trade_direction) # Nihai yönü geçir
                    # Skorları işle (self.scores[symbol] var olduğu için artık uyarı vermemeli)
                    for score, detail in other_timeframe_scores:
                        if score > 0:
                            self.scores[symbol]["confirmation_score"] += score
                            self.scores[symbol]["confirmation_details"].append(detail)
                        elif score < 0:
                            self.scores[symbol]["negative_score"] += score
                            self.scores[symbol]["negative_details"].append(detail)
                    logger.info(f"[{symbol}] Diğer zaman dilimlerinden {len(other_timeframe_scores)} puan eklendi (Conf/Neg olarak ayrıştırıldı).")
                 except Exception as e:
                    logger.error(f"[{symbol}] Diğer zaman dilimleri puanlama hatası: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

            # --- 7. Genel Toplam Skoru Hesapla ---
            if timeframe == self.main_timeframe and symbol in self.scores:
                final_base_score = self.scores[symbol].get("base_score", 0.0)
                final_conf_score = self.scores[symbol].get("confirmation_score", 0.0)
                final_neg_score = self.scores[symbol].get("negative_score", 0.0) # Bu artık PD çakışmalarını da içeriyor
                net_score = final_base_score + final_conf_score + final_neg_score
                self.scores[symbol]["net_score"] = net_score
                logger.info(f"[{symbol} {timeframe}] NET PUAN HESAPLANDI: {net_score:.2f} (Base: {final_base_score:.2f}, Conf: {final_conf_score:.2f}, Neg: {final_neg_score:.2f})")

                # --- Trade Seviyeleri (Nihai yön ile) ---
                logger.debug(f"[{symbol}] Trade seviyeleri hesaplanıyor...")

                # Akıllı giriş stratejisinden entry_levels hesapla
                # smart_entry_strategy.calculate_entry_levels çağrısına candles parametresini ekle
                entry_levels = self.smart_entry_strategy.calculate_entry_levels(
                    symbol=symbol,  
                    stats=stats,
                    trade_direction=main_trade_direction,
                    fibonacci_data=fib_data_4h,
                    swing_points=all_timeframe_data.get(symbol, {}).get(timeframe, {}).get('swing_points'),
                    pattern_name=base_pattern_details[0][1] if base_pattern_details else None,  # base_details -> base_pattern_details olarak düzeltildi
                    candles=candles, # <-- YENİ EKLENEN PARAMETRE
                    fvg_data=fvg_data,
                    order_blocks=order_blocks
                )

                # entry_levels'ı score_result'a ekle
                self.scores[symbol]['entry_levels'] = entry_levels
                logger.debug(f"[{symbol}] Akıllı giriş seviyeleri hesaplandı: {entry_levels}")

                # Trade seviyelerini ve yüzdeleri `entry_levels` sonucundan ayıkla
                entry = entry_levels.get('entry') or entry_levels.get('primary_entry')
                sl = entry_levels.get('sl') or entry_levels.get('stop_loss')
                tp1 = entry_levels.get('tp1')
                
                sl_pct = None
                tp_pct = None

                if entry and sl:
                    sl_pct = abs((sl - entry) / entry) * 100
                if entry and tp1:
                    tp_pct = abs((tp1 - entry) / entry) * 100 # TP1'e göre R/R

                # Geriye dönük uyumluluk ve raporlama için anahtar değerleri ayarla
                self.scores[symbol]['entry_price'] = entry
                self.scores[symbol]['sl_price'] = sl
                self.scores[symbol]['tp_price'] = tp1  # Ana TP olarak TP1 kullanılıyor
                self.scores[symbol]['sl_pct'] = sl_pct
                self.scores[symbol]['tp_pct'] = tp_pct
                self.scores[symbol]['tp1_price'] = tp1
                self.scores[symbol]['tp2_price'] = entry_levels.get('tp2')
                self.scores[symbol]['tp3_price'] = entry_levels.get('tp3')

                # Ek rapor bilgilerini kaydet
                self.scores[symbol]['strategy_used'] = entry_levels.get('strategy', 'default')
                
                # Volatility bilgisini ekle (eğer stats'ta varsa)
                if stats and 'volatility_level' in stats:
                    self.scores[symbol]['volatility_level'] = stats['volatility_level']
                elif stats and 'volatility' in stats:
                    # Volatility değerini seviyeye dönüştür
                    volatility = stats.get('volatility', 0)
                    if volatility > 0.05:  # %5'ten fazla
                        self.scores[symbol]['volatility_level'] = 'high'
                    elif volatility > 0.02:  # %2-5 arası
                        self.scores[symbol]['volatility_level'] = 'medium'
                    else:
                        self.scores[symbol]['volatility_level'] = 'low'
                
                # Fibonacci seviyesi bilgisini ekle (eğer fibonacci_data'da varsa)
                if fib_data_4h and 'current_level' in fib_data_4h:
                    self.scores[symbol]['fib_level'] = fib_data_4h['current_level']
                elif fib_data and 'current_level' in fib_data:
                    self.scores[symbol]['fib_level'] = fib_data['current_level']

                # --- YENİ KONTROL: Minimum Risk Kontrolü (Sinyal Filtresi) ---
                # Eğer stop mesafesi çok darsa, sinyali geçersiz say ve raporlama.
                MIN_RISK_THRESHOLD_PCT = 0.8
                
                if sl_pct is not None and sl_pct < MIN_RISK_THRESHOLD_PCT:
                    logger.warning(
                        f"[{symbol}] Sinyal REDDEDİLDİ: Stop mesafesi (%{sl_pct:.2f}) "
                        f"minimum eşiğin (%{MIN_RISK_THRESHOLD_PCT}) altında."
                    )
                    # Bu sembol için tüm skorları ve bilgileri temizleyerek sinyalin oluşmasını engelle
                    if symbol in self.scores:
                        del self.scores[symbol]
                    
                    # DİKKAT: Ana döngüye sinyalin neden reddedildiğini bildiriyoruz.
                    return {"status": "REJECTED_LOW_RISK", "symbol": symbol, "risk_pct": sl_pct}

                # Kullanılan strateji bilgisini logla
                strategy_used = entry_levels.get("strategy", "default")
                strategy_text = f", Strateji: {strategy_used}"

                logger.debug(f"[{symbol}] Trade seviyeleri ayarlandı: Entry={self.format_price(entry)}, SL={self.format_price(sl)}, TP1={self.format_price(tp1)}{strategy_text}")

        except Exception as e:
            logger.error(f"[{symbol} {timeframe}] Puanlama sırasında genel hata: {e}")
            # ... (outer exception handling) ...

        # --- Sonuç Döndürme ---
        if timeframe == self.main_timeframe and symbol in self.scores:
            return self.scores[symbol]
        else:
            # Diğer zaman dilimleri veya hata durumunda boş/varsayılan döndür
            return {
                 "total_score": 0.0,
                 "scores": [],
                 "errors": [] if symbol in self.scores else ["Skor hesaplanmadı (Ana zaman dilimi değil)."]
            }

    def _get_timeframe_label(self, timeframe: str) -> str:
        """
        Zaman diliminin görüntüleme etiketini döndürür.

        Args:
            timeframe (str): Zaman dilimi kodu (örn: '1d', '4h')

        Returns:
            str: Görüntüleme etiketi (örn: 'D', '4H')
        """
        if timeframe == '1d':
            return 'D'
        elif timeframe == '4h':
            return '4H'
        elif timeframe == '1h':
            return '1H'
        else:
            # Bilinmeyen timeframe için orijinal değeri büyük harfle kullan
            return timeframe.upper()

    def _get_main_direction(self, symbol: str) -> Optional[str]:
        """
        Ana timeframe'deki pattern tiplerini inceleyerek işlem yönünü belirler.
        Tek tip formasyonlar varsa bu pattern tipi kullanılacak:
        - Bullish formasyonlar varsa ve bearish formasyonlar yoksa "bull"
        - Bearish formasyonlar varsa ve bullish formasyonlar yoksa "bear"
        - Karışık formasyonlar varsa veya hiç formasyon yoksa None

        Args:
            symbol: Sembol (örn: "BTCUSDT")

        Returns:
            Optional[str]: İşlem yönü ("bull", "bear" veya None)
        """
        if symbol not in self.scores:
            return None

        base_details = self.scores[symbol].get("base_details", [])

        has_bull_patterns = any(detail[0].startswith("Bull") for detail in base_details if isinstance(detail, tuple))
        has_bear_patterns = any(detail[0].startswith("Bear") for detail in base_details if isinstance(detail, tuple))

        if has_bull_patterns and not has_bear_patterns:
            return "bull"
        elif has_bear_patterns and not has_bull_patterns:
            return "bear"
        return None

    def _determine_direction_from_patterns(self, base_details: List[Tuple[str, str, float]]) -> Optional[str]:
        """
        Verilen base_details listesindeki pattern tiplerine göre işlem yönünü belirler.

        Args:
            base_details (List[Tuple[str, str, float]]): _score_patterns'dan dönen detay listesi.
                                                          Format: [("Bull PAT", "Pattern Name", Score), ...]

        Returns:
            Optional[str]: İşlem yönü ("bull", "bear" veya None)
        """
        if not base_details: # Eğer detay listesi boşsa None dön
             return None

        # Pattern tiplerini kontrol et
        has_bull_patterns = any(detail[0].startswith("Bull") for detail in base_details if isinstance(detail, tuple))
        has_bear_patterns = any(detail[0].startswith("Bear") for detail in base_details if isinstance(detail, tuple))

        # Yönü belirle
        if has_bull_patterns and not has_bear_patterns:
            return "bull"
        elif has_bear_patterns and not has_bull_patterns:
            return "bear"
        else: # Karışık veya bilinmeyen durum
            return None

    def _score_patterns(self, symbol: str, timeframe: str, patterns: List[Dict[str, Any]], bos_results: Dict[str, Any], trade_direction: Optional[str] = None) -> Tuple[float, List[Tuple[str, str, float]], List[Tuple[str, float]]]:
        """
        Temel puanı hesaplar (sadece formasyonlar için)
        Sadece 4h timeframe için kullanılır
        Premium/Discount bölgelerinde pattern'lar için ek puanlama yapar

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "240")
            patterns: Tespit edilen formasyonlar listesi
            bos_results: BOS analiz sonuçları (kullanılmıyor)
            trade_direction: İşlem yönü (bull/bear), None ise pattern tipinden çıkarılır

        Returns:
            Tuple[float, List[Tuple[str, str, float]], List[Tuple[str, float]]]:
                Temel puan, temel puan detayları ve premium/discount çakışma detayları
        """
        base_score = 0.0
        base_details = []  # Detayları (Pattern Tipi, Pattern Adı, Skor) olarak saklayalım
        pd_conflict_details = [] # Premium/Discount çakışmalarını (Açıklama, Skor) olarak saklayalım
        pattern_count = len(patterns) if patterns else 0
        logger.debug(f"[{symbol}] _score_patterns called: {pattern_count} patterns, timeframe: {timeframe}")

        if timeframe == self.main_timeframe:  # Sadece 4h için
            if not patterns:
                logger.info(f"[{symbol} {timeframe}] Pattern bulunamadı, temel puan 0.0 olarak ayarlandı.")
                return 0.0, [], []

            # Premium/Discount bilgilerini al
            pd_zones = self.scores.get(symbol, {}).get("pd_zones")

            # pd_zones None ise, boş bir sözlük oluştur
            if pd_zones is None:
                logger.debug(f"[{symbol}] Premium/Discount bilgileri bulunamadı, çakışma kontrolü yapılmayacak.")
                pd_zones = {}

            for pattern in patterns:
                pattern_name = pattern.get("name", "Unknown")
                pattern_type = pattern.get("type", "").upper()
                logger.debug(f"[{symbol} {timeframe}] Pattern değerlendiriliyor: {pattern_name}, type: {pattern_type}")

                # Pattern tipine göre puan ver
                if "BOS" in pattern_name.upper():
                    pattern_score = 5.0
                    logger.debug(f"[{symbol} {timeframe}] BOS pattern tespit edildi: {pattern_name}, skor: +{pattern_score}")
                elif "TRIB" in pattern_name.upper() or "TRIT" in pattern_name.upper():
                    pattern_score = 4.0
                    logger.debug(f"[{symbol} {timeframe}] TRIB/TRIT pattern tespit edildi: {pattern_name}, skor: +{pattern_score}")
                else:
                    pattern_score = 3.0

                base_score += pattern_score # Pattern skorunu base_score'a ekle

                if pattern_type == "BULLISH":
                    base_details.append(("Bull PAT", pattern_name, pattern_score))
                    logger.debug(f"[{symbol} {timeframe}] Bullish Pattern eklendi: {pattern_name}, skor: +{pattern_score}")
                elif pattern_type == "BEARISH":
                    base_details.append(("Bear PAT", pattern_name, pattern_score))
                    logger.debug(f"[{symbol} {timeframe}] Bearish Pattern eklendi: {pattern_name}, skor: +{pattern_score}")
                else:
                    logger.warning(f"[{symbol} {timeframe}] Bilinmeyen pattern tipi: {pattern_type}")

                # Premium/Discount puanlaması artık yeni sistem (score_structure_premium_discount) tarafından yapılıyor
                # Ana timeframe çakışma kontrolü kaldırıldı - çakışmayı önlemek için

            logger.debug(f"[{symbol} {timeframe}] Hesaplanan temel skor: {base_score}, Temel Detaylar: {base_details}, PD Çakışmaları: {pd_conflict_details}")

        return base_score, base_details, pd_conflict_details

    def _score_patterns_other_tf(self, symbol: str, timeframe: str, patterns: List[Dict[str, Any]], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        Diğer zaman dilimleri için pattern puanlaması

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "D")
            patterns: Formasyonlar
            trade_direction: Bull veya bear pattern varlığına göre belirlenen işlem yönü

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if timeframe not in ["D", "720", "60"] or not patterns:
            return scores

        tf_label = self._get_timeframe_label(timeframe)

        # Zaman dilimine göre temel puan değeri
        base_pattern_scores = {"D": 1.0, "720": 0.75, "60": 1.0}
        base_pattern_score = base_pattern_scores.get(timeframe, 1.0)

        # Premium/Discount bilgilerini al
        pd_zones = self.scores.get(symbol, {}).get("pd_zones")

        # pd_zones None ise, boş bir sözlük oluştur
        if pd_zones is None:
            logger.debug(f"[{symbol}] Premium/Discount bilgileri bulunamadı, çakışma kontrolü yapılmayacak.")
            pd_zones = {}

        # Her pattern için kontrol
        for pattern in patterns:
            pattern_name = pattern.get("name", "")
            pattern_type = pattern.get("type", "").upper()

            # Pattern tipine göre puan hesapla
            if "TRIB" in pattern_name.upper() or "TRIT" in pattern_name.upper():
                # TRIB/TRIT patternleri için özel puan (4.0 * timeframe multiplier)
                pattern_score = 4.0 * base_pattern_score
                logger.debug(f"[{symbol} {tf_label}] TRIB/TRIT pattern tespit edildi: {pattern_name}, skor: +{pattern_score}")
            else:
                # Diğer patternler için normal puan
                pattern_score = base_pattern_score

            # Yön uyumluluğu kontrolü artık doğrudan pattern tipi üzerinden yapılıyor
            if (pattern_type == "BULLISH" and trade_direction == "bull") or (pattern_type == "BEARISH" and trade_direction == "bear"):
                # Emoji ekle ve formatı kısalt
                direction_emoji = "⬆️" if pattern_type == "BULLISH" else "⬇️"
                scores.append((pattern_score, f"{direction_emoji} {tf_label} {pattern_name}"))
                logger.debug(f"[{symbol} {tf_label}] Direction-aligned Pattern score added: +{pattern_score} ({pattern_name})")

                # Premium/Discount puanlaması artık yeni sistem (score_structure_premium_discount) tarafından yapılıyor
                # Pattern bazlı Premium/Discount puanlaması kaldırıldı - çakışmayı önlemek için

        return scores

    def _score_order_blocks(self, symbol: str, timeframe: str, order_blocks: Dict[str, Any], stats: Dict[str, Any], trade_direction: Optional[str] = None) -> List[Tuple[float, str]]:
        """
        Order Block'lara göre puanlama yapar.
        - Fiyat SADECE OB içindeyse: Ana yönle OB tipinin uyumuna göre +/- 1.0 puan alır.
        - Fiyat OB dışındaysa (Premium/Discount): Puan verilmez.

        Args:
            symbol (str): Kripto para sembolü
            timeframe (str): Zaman dilimi (örn. '720', '240')
            order_blocks (Dict): Order Block analiz sonuçları ('bullish', 'bearish' anahtarları)
            stats (Dict): Fiyat bilgileri ('last_price')
            trade_direction (str, optional): Ana işlem yönü ('bull' veya 'bear'). Defaults to None.

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []
        if not order_blocks or not stats or not trade_direction:
            logger.debug(f"[{symbol} {timeframe}] OB skorlaması atlandı: OB={bool(order_blocks)}, Stats={bool(stats)}, Direction={trade_direction}")
            return scores

        last_price = stats.get("last_price")
        if not last_price:
            return scores

        tf_label = self._get_timeframe_label(timeframe)
        # Puanlar
        interaction_score = 1.0 # OB içi etkileşim için standart puan (+/-)

        # Bullish OB (Demand) kontrolü
        bullish_ob = order_blocks.get('bullish')
        if bullish_ob:
            ob_low = bullish_ob.get('low')
            ob_high = bullish_ob.get('high')
            ob_label = f"{tf_label} Bullish OB"

            if ob_low and ob_high:
                # --- Fiyat SADECE OB içinde mi kontrolü ---
                if ob_low <= last_price <= ob_high:
                    # Yön Kontrollü Puanlama (Tüm TF'ler için)
                    if trade_direction == "bull":
                        scores.append((interaction_score, f"✅ In {ob_label} (Bull Dir)"))
                    elif trade_direction == "bear":
                        scores.append((-interaction_score, f"🔴 In {ob_label} (Bear Dir)"))
                # --- Fiyat OB dışında ise (Premium/Discount) puanlama yok ---


        # Bearish OB (Supply) kontrolü
        bearish_ob = order_blocks.get('bearish')
        if bearish_ob:
            ob_low = bearish_ob.get('low')
            ob_high = bearish_ob.get('high')
            ob_label = f"{tf_label} Bearish OB"

            if ob_low and ob_high:
                 # --- Fiyat SADECE OB içinde mi kontrolü ---
                if ob_low <= last_price <= ob_high:
                    # Yön Kontrollü Puanlama (Tüm TF'ler için)
                    if trade_direction == "bear":
                        scores.append((interaction_score, f"✅ In {ob_label} (Bear Dir)"))
                    elif trade_direction == "bull":
                        scores.append((-interaction_score, f"🔴 In {ob_label} (Bull Dir)"))
                # --- Fiyat OB dışında ise (Premium/Discount) puanlama yok ---

        return scores

    def _score_bos_other_tf(self, symbol: str, timeframe: str, bos_results: Dict[str, Any], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        Diğer zaman dilimleri için BOS puanlaması

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "D")
            bos_results: BOS sonuçları
            trade_direction: Bull veya bear pattern varlığına göre belirlenen işlem yönü

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if timeframe not in ["720", "60", "D"] or not bos_results or not trade_direction:
            logger.debug(f"[{symbol}] BOS skorlaması atlandı: timeframe={timeframe}, bos_results={bool(bos_results)}, trade_direction={trade_direction}")
            return scores

        tf_label = self._get_timeframe_label(timeframe)

        # BOS puanı - Zaman dilimine göre farklı değerler
        bos_score_val = 0
        if timeframe == "60":
            bos_score_val = 1.0
        elif timeframe == "720":
            bos_score_val = 1.5
        elif timeframe == "D":
            bos_score_val = 2.0  # Günlük BOS için daha yüksek puan

        # BOS anahtarlarını logla
        logger.info(f"[{symbol}] BOS analiz sonuçları: {list(bos_results.keys())} (timeframe={timeframe})")

        # BOS yön kontrolü
        bullish_bos = bos_results.get("bullish", None)
        bearish_bos = bos_results.get("bearish", None)

        # BOS sonuçlarını logla
        if bullish_bos:
            logger.info(f"[{symbol}] Bullish BOS bulundu, timeframe={timeframe}")
        if bearish_bos:
            logger.info(f"[{symbol}] Bearish BOS bulundu, timeframe={timeframe}")

        if bullish_bos and trade_direction == "bull":
            scores.append((bos_score_val, f"{tf_label} Bullish BOS"))
            logger.info(f"[{symbol} {tf_label}] Yön uyumlu Bullish BOS puanı eklendi: +{bos_score_val}")
        elif bearish_bos and trade_direction == "bear":
            scores.append((bos_score_val, f"{tf_label} Bearish BOS"))
            logger.info(f"[{symbol} {tf_label}] Yön uyumlu Bearish BOS puanı eklendi: +{bos_score_val}")

        return scores

    def _score_ema(self, symbol: str, timeframe: str, stats: Optional[Dict[str, Any]], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        EMA puanlaması - Her EMA için 0.5 puan

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "D")
            stats: İstatistik verileri
            trade_direction: Bull veya bear pattern varlığına göre belirlenen işlem yönü

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if not stats or not trade_direction:
            return scores

        last_price = stats.get("last_price")
        if not last_price:
            return scores

        # EMA periyotları için puanlama - Her EMA 0.5 puan
        for ema_period in [26, 50, 100]:
            ema_val = stats.get(f"ema{ema_period}")
            ema_pct = stats.get(f"ema{ema_period}_pct")

            if ema_val is not None and ema_pct is not None:
                # Pozitif puanlama - Bullish yönde fiyat EMA üzerinde
                if ema_pct > 0 and trade_direction == "bull":
                    scores.append((0.5, f"EMA{ema_period}⬆️"))
                    logger.debug(f"[{symbol}] EMA Score: +0.5 (Price above EMA{ema_period} and bull direction)")
                # Pozitif puanlama - Bearish yönde fiyat EMA altında
                elif ema_pct < 0 and trade_direction == "bear":
                    scores.append((0.5, f"EMA{ema_period}⬇️"))
                    logger.debug(f"[{symbol}] EMA Score: +0.5 (Price below EMA{ema_period} and bear direction)")

                # Negatif puanlama - Bullish yönde fiyat EMA altında
                elif ema_pct < 0 and trade_direction == "bull":
                    scores.append((-0.5, f"🔴 Neg EMA{ema_period}"))
                    logger.debug(f"[{symbol}] Negative Score: -0.5 (Bull PAT Below EMA{ema_period})")

                # Negatif puanlama - Bearish yönde fiyat EMA üstünde
                elif ema_pct > 0 and trade_direction == "bear":
                    scores.append((-0.5, f"🔴 Neg EMA{ema_period}"))
                    logger.debug(f"[{symbol}] Negative Score: -0.5 (Bear PAT Above EMA{ema_period})")

        return scores

    def _score_vwap(self, symbol: str, timeframe: str, stats: Optional[Dict[str, Any]], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        VWAP puanlaması

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "D")
            stats: İstatistik verileri
            trade_direction: Bull veya bear pattern varlığına göre belirlenen işlem yönü

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if not stats or not trade_direction:
            return scores

        vwap = stats.get("vwap")
        vwap_diff = stats.get("vwap_diff")
        last_price = stats.get("last_price")

        if vwap is None or vwap_diff is None or last_price is None:
            return scores

        # Pozitif puanlama
        if vwap_diff > 0 and trade_direction == "bull":
            scores.append((1.0, "VWAP⬆️"))
        elif vwap_diff < 0 and trade_direction == "bear":
            scores.append((1.0, "VWAP⬇️"))

        # Negatif puanlama
        if vwap_diff < 0 and trade_direction == "bull":
            scores.append((-1.0, "🔴 Neg VWAP"))
            logger.debug(f"[{symbol}] Negative Score: -0.5 (Bull PAT Below VWAP)")
        elif vwap_diff > 0 and trade_direction == "bear":
            scores.append((-1.0, "🔴 Neg VWAP"))
            logger.debug(f"[{symbol}] Negative Score: -0.5 (Bear PAT Above VWAP)")

        return scores

    def _score_timeframe_levels(self, symbol: str, timeframe: str, timeframe_levels: Optional[Dict[str, Any]], stats: Optional[Dict[str, Any]], price_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        Timeframe Levels kesişimi puanlaması

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "D")
            timeframe_levels: Timeframe seviyeleri
            stats: İstatistik verileri
            price_direction: İşlem yönü (bull/bear)

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if not timeframe_levels or not stats or not price_direction:
            logger.debug(f"[{symbol}] Timeframe levels skorlaması atlandı: timeframe_levels={bool(timeframe_levels)}, stats={bool(stats)}, direction={price_direction}")
            return scores

        last_price = stats.get("last_price")
        if not last_price:
            return scores

        # Timeframe Levels yapısını logla
        logger.info(f"[{symbol}] Timeframe Levels anahtarları: {list(timeframe_levels.keys())} (timeframe={timeframe})")

        # Seviye kontrolleri ve emojiler
        level_checks = {
            'wo': ("🟦WO", "Weekly Open"),
            'pwo': ("🟨PWO", "Previous Weekly Open"),
            'mo': ("🟪MO", "Monthly Open"),
            'pmo': ("🟫PMO", "Previous Monthly Open")
        }

        # Her seviye için kontrol yap
        for level_key, (emoji, label) in level_checks.items():
            level_value = timeframe_levels.get(level_key)

            if level_value:
                logger.debug(f"[{symbol}] {label} kontrol ediliyor: Fiyat {last_price:.4f}, Seviye {level_value:.4f}")

                # Pozitif puanlama - yönle uyumlu
                if last_price > level_value and price_direction == "bull":
                    scores.append((0.5, f"{emoji}⬆️"))
                    logger.info(f"[{symbol}] {label} uyumlu: +0.5 puan eklendi (Fiyat > {level_key})")
                elif last_price < level_value and price_direction == "bear":
                    scores.append((0.5, f"{emoji}⬇️"))
                    logger.info(f"[{symbol}] {label} uyumlu: +0.5 puan eklendi (Fiyat < {level_key})")

                # Negatif puanlama - yönle uyumsuz
                if last_price < level_value and price_direction == "bull":
                    scores.append((-0.5, f"🔴 Neg {level_key.upper()}"))
                    logger.debug(f"[{symbol}] Negatif Skor: -0.5 (Bull Yön Fakat Fiyat < {level_key})")
                elif last_price > level_value and price_direction == "bear":
                    scores.append((-0.5, f"🔴 Neg {level_key.upper()}"))
                    logger.debug(f"[{symbol}] Negatif Skor: -0.5 (Bear Yön Fakat Fiyat > {level_key})")

        # Ranges kontrolü
        if 'ranges' in timeframe_levels and timeframe_levels['ranges']:
            ranges = timeframe_levels['ranges']
            for i, range_data in enumerate(ranges):
                if range_data.get('start') <= last_price <= range_data.get('end'):
                    range_type = range_data.get('type', 'unknown')
                    range_emoji = "📊"
                    scores.append((0.5, f"{range_emoji} Range {range_type}"))
                    logger.info(f"[{symbol}] Fiyat Range içinde: +0.5 puan eklendi (Tip: {range_type})")

        return scores

    def _score_eq_levels(self, symbol: str, timeframe: str, timeframe_levels: Optional[Dict[str, Any]], stats: Optional[Dict[str, Any]], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        EQ seviyeleri puanlaması

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "D")
            timeframe_levels: Timeframe seviyeleri
            stats: İstatistik verileri
            trade_direction: Bull veya bear pattern varlığına göre belirlenen işlem yönü

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if not timeframe_levels or not stats or not trade_direction:
            return scores

        last_price = stats.get("last_price")
        if not last_price:
            return scores

        # EQ seviyeleri kontrolü
        eq_checks = {
            'eqd': ("🟨EQD", "Daily EQ"),
            'eqw': ("🟦EQW", "Weekly EQ"),
            'eqm': ("🟣EQM", "Monthly EQ"),
            'mreq': ("📅MREQ", "Monday Range EQ")
        }

        for eq_key, (emoji, label) in eq_checks.items():
            eq_level = timeframe_levels.get(eq_key)

            if eq_level:
                if eq_key == 'mreq':
                    logger.info(f"{symbol} - MREq control: Last price {self.format_price(last_price)} - MREq value {self.format_price(eq_level)}")

                # Pozitif puanlama
                if last_price > eq_level and trade_direction == "bull":
                    scores.append((0.5, f"{emoji}⬆️"))
                    if eq_key == 'mreq':
                        logger.info(f"{symbol} - MREq: +0.5 point added (Price above MREq and bullish direction)")
                elif last_price < eq_level and trade_direction == "bear":
                    scores.append((0.5, f"{emoji}⬇️"))
                    if eq_key == 'mreq':
                        logger.info(f"{symbol} - MREq: +0.5 point added (Price below MREq and bear direction)")

                # Negatif puanlama
                if last_price < eq_level and trade_direction == "bull":
                    scores.append((-0.5, f"🔴 Neg {eq_key.upper()}"))
                    logger.debug(f"[{symbol}] Negative Score: -0.5 (Bull PAT Below {eq_key.upper()})")
                elif last_price > eq_level and trade_direction == "bear":
                    scores.append((-0.5, f"🔴 Neg {eq_key.upper()}"))
                    logger.debug(f"[{symbol}] Negative Score: -0.5 (Bear PAT Above {eq_key.upper()})")

        return scores

    def _score_divergence(self, symbol: str, timeframe: str, divergences: Optional[List[Dict[str, Any]]], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        Divergence puanlaması

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "D")
            divergences: Divergence listesi
            trade_direction: Bull veya bear pattern varlığına göre belirlenen işlem yönü

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        # 720 ve D zaman dilimlerinde divergence puanlaması yapma
        if timeframe == "720" or timeframe == "D":
            logger.info(f"[{symbol}] {timeframe} zaman diliminde divergence puanlaması atlanıyor.")
            return scores

        if not divergences or not trade_direction:
            return scores

        tf_label = self._get_timeframe_label(timeframe)
        divergence_score = 0.5
        negative_divergence_score = -0.5

        # Her divergence için
        for div in divergences:
            div_type = div.get("type", "")
            # "Hidden" içerip içermediğini kontrol et
            hidden_prefix = "Hid " if "Hidden" in div_type else ""

            # Pozitif puanlama - yön uyumlu
            if ("Positive" in div_type or "Bullish" in div_type) and trade_direction == "bull":
                # Etiketi kısalt (örn: ✅ 4s Hid Bull Div)
                label = f"✅ {tf_label} {hidden_prefix}Bull Div"
                scores.append((divergence_score, label))
                logger.debug(f"[{symbol} {tf_label}] Direction-aligned Divergence score added: +{divergence_score} ({div_type})")
            elif ("Negative" in div_type or "Bearish" in div_type) and trade_direction == "bear":
                # Etiketi kısalt (örn: ✅ 4s Hid Bear Div)
                label = f"✅ {tf_label} {hidden_prefix}Bear Div"
                scores.append((divergence_score, label))
                logger.debug(f"[{symbol} {tf_label}] Direction-aligned Divergence score added: +{divergence_score} ({div_type})")

            # Negatif puanlama
            elif ("Positive" in div_type or "Bullish" in div_type) and trade_direction == "bear":
                # Etiketi kısalt ve "Hidden" bilgisini ekle
                label = f"🔴 Neg {tf_label} {hidden_prefix}Bull Div"
                scores.append((negative_divergence_score, label))
                logger.debug(f"[{symbol}] Negative Score: {negative_divergence_score:.1f} (Bear PAT Reversed {hidden_prefix}Bull Div)")
            elif ("Negative" in div_type or "Bearish" in div_type) and trade_direction == "bull":
                 # Etiketi kısalt ve "Hidden" bilgisini ekle
                label = f"🔴 Neg {tf_label} {hidden_prefix}Bear Div"
                scores.append((negative_divergence_score, label))
                logger.debug(f"[{symbol}] Negative Score: {negative_divergence_score:.1f} (Bull PAT Reversed {hidden_prefix}Bear Div)")

        return scores

    def _score_sfp(self, symbol: str, timeframe: str, sfps: Optional[List[Dict[str, Any]]], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        Swing Failure Pattern (SFP) puanlaması

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "D")
            sfps: SFP listesi
            trade_direction: Bull veya bear pattern varlığına göre belirlenen işlem yönü

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if not sfps or not trade_direction:
            return scores

        # Zaman dilimi etiketini al
        tf_label = self._get_timeframe_label(timeframe)

        # Sadece ilgili timeframe'deki SFP'leri filtrele
        timeframe_sfps = [sfp for sfp in sfps if sfp.get("timeframe") == timeframe]

        if not timeframe_sfps:
            logger.debug(f"[{symbol}] {timeframe} timeframe'inde SFP bulunamadı.")
            return scores

        # Son oluşmuş SFP'yi al
        for sfp in timeframe_sfps:
            sfp_type = sfp.get("sfp_type", "")

            # Pozitif puanlama - yön uyumlu
            if sfp_type == "Bullish" and trade_direction == "bull":
                scores.append((1.0, "⬆️ Bullish SFP"))
                logger.debug(f"[{symbol}] Pozitif SFP puanı: +1.0 (Bullish SFP ve bull yönü)")
                break
            elif sfp_type == "Bearish" and trade_direction == "bear":
                scores.append((1.0, "⬇️ Bearish SFP"))
                logger.debug(f"[{symbol}] Pozitif SFP puanı: +1.0 (Bearish SFP ve bear yönü)")
                break

        # Negatif puanlama - yön uyumsuz
        # Pozitif puanlamada olduğu gibi, sadece son oluşmuş SFP'yi al
        for sfp in timeframe_sfps:
            sfp_type = sfp.get("sfp_type", "")

            # Negatif puanlama - yön uyumsuz
            if sfp_type == "Bullish" and trade_direction == "bear":
                scores.append((-1.0, f"🔴 Neg SFP: Bull ({tf_label})"))
                logger.debug(f"[{symbol}] Negatif SFP puanı: -1.0 (Bear yönü fakat Bullish SFP)")
                break
            elif sfp_type == "Bearish" and trade_direction == "bull":
                scores.append((-1.0, f"🔴 Neg SFP: Bear ({tf_label})"))
                logger.debug(f"[{symbol}] Negatif SFP puanı: -1.0 (Bull yönü fakat Bearish SFP)")
                break

        return scores

    def _score_naked_poc(self, symbol: str, timeframe: str, naked_pocs: Optional[List[Dict[str, Any]]], stats: Optional[Dict[str, Any]]) -> List[Tuple[float, str]]:
        """
        Naked POC puanlaması

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if not naked_pocs or not stats:
            return scores

        last_price = stats.get("last_price")
        if not last_price:
            return scores

        tf_label = self._get_timeframe_label(timeframe)
        proximity_threshold_pct = 2.0  # %2 tolerans

        # Her POC için kontrol
        for poc in naked_pocs:
            poc_price = poc.get("price")
            if poc_price:
                lower_bound = poc_price * (1 - proximity_threshold_pct / 100)
                upper_bound = poc_price * (1 + proximity_threshold_pct / 100)

                if lower_bound <= last_price <= upper_bound:
                    scores.append((1.0, f"{tf_label} Naked POC Intersection"))
                    logger.debug(f"[{symbol} {tf_label}] Direction-aligned Naked POC score added: +1.0 (Tolerance: %{proximity_threshold_pct})")
                    break  # En yakın uygun POC için puan ver

        return scores

    def _score_funding_rate(self, symbol: str, timeframe: str, stats: Optional[Dict[str, Any]], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        Funding Rate puanlaması

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            timeframe: Zaman dilimi (örn: "D")
            stats: İstatistik verileri
            trade_direction: Bull veya bear pattern varlığına göre belirlenen işlem yönü

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if not stats or not trade_direction:
            return scores

        funding_rate = stats.get("funding_rate")
        if funding_rate is None:
            return scores

        # Pozitif puanlama
        if funding_rate < 0 and trade_direction == "bull":
            scores.append((0.5, f"✅ Fund: Neg {funding_rate:.4f} ⬆️"))
            logger.debug(f"{symbol} {timeframe}: Negative Funding (Bullish Signal): +0.5")
        elif funding_rate > 0 and trade_direction == "bear":
            scores.append((0.5, f"✅ Fund: Pos {funding_rate:.4f} ⬇️"))
            logger.debug(f"{symbol} {timeframe}: Positive Funding (Bearish Signal): +0.5")

        # Negatif puanlama - yüksek uyumsuz funding rate
        if trade_direction == "bull" and funding_rate > 0.005:  # %0.5'den büyük pozitif funding (Bearish sinyal)
            # Funding büyüklüğüne göre dinamik etki
            fr_impact = min(1.5, max(0.5, abs(funding_rate) * 50))
            fr_neg_points = -0.5 * fr_impact
            fr_neg_points = max(-2.0, fr_neg_points)  # En fazla -2.0 puan

            scores.append((fr_neg_points, f"🔴 Neg Fund: Pos {funding_rate:.4f}"))
            logger.debug(f"[{symbol}] Negative Score: {fr_neg_points:.2f} (Bull PAT + Pos. Funding: {funding_rate:.4f})")
        elif trade_direction == "bear" and funding_rate < -0.005:  # %0.5'den düşük negatif funding (Bullish sinyal)
            # Funding büyüklüğüne göre dinamik etki
            fr_impact = min(1.5, max(0.5, abs(funding_rate) * 50))
            fr_neg_points = -0.5 * fr_impact
            fr_neg_points = max(-2.0, fr_neg_points)  # En fazla -2.0 puan

            scores.append((fr_neg_points, f"🔴 Neg Fund: Neg {funding_rate:.4f}"))
            logger.debug(f"[{symbol}] Negative Score: {fr_neg_points:.2f} (Bear PAT + Neg. Funding: {funding_rate:.4f})")

        return scores

    def _score_supertrend(self, symbol: str, timeframe: str, supertrend: Optional[Dict[str, Any]], price_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        SuperTrend puanlaması

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if not supertrend or not price_direction:
            return scores

        st_trend = supertrend.get("trend")
        if not st_trend:
            return scores

        # Trend uyumu kontrolü
        st_bull = st_trend == "up"
        base_bull = price_direction == "bull"
        st_emoji = "⬆️" if st_trend == "up" else "⬇️"

        # SuperTrend sinyalini al
        st_signal = supertrend.get("signal", "").lower()

        # Pozitif puanlama - trend uyumu
        if (st_bull and base_bull) or (not st_bull and not base_bull):
            scores.append((0.5, f"🟢 STrend Align ST{st_emoji}"))
            logger.debug(f"[{symbol}] SuperTrend trend uyumu: +0.5 puan (ST: {st_trend}, Pattern: {price_direction})")

            # Mesafe faktörü - yakın trendin başlangıcı
            st_distance_pct = supertrend.get("distance_percent", 0)
            if st_distance_pct < 2.0:
                scores.append((0.5, f"📱 STrend Close ({st_distance_pct:.2f}%)"))
                logger.debug(f"[{symbol}] SuperTrend mesafe puanı: +0.5 (Mesafe: {st_distance_pct:.2f}%)")

            # Yeni sinyal puanı - SADECE GERÇEK SİNYALLER İÇİN PUAN EKLE
            # "neutral", "nötr", "notr" gibi farklı yazımları kontrol et
            if st_signal and st_signal not in ["neutral", "nötr", "notr", "ntr", "n/a", "none"]:
                scores.append((0.5, f"STrend Signal ({st_signal})"))
                logger.debug(f"[{symbol}] SuperTrend sinyal puanı: +0.5 (Sinyal: {st_signal})")
            else:
                logger.debug(f"[{symbol}] SuperTrend sinyali nötr veya yok, puan eklenmedi. Sinyal: {st_signal}")

        # Negatif puanlama - trend uyumsuzluğu
        if (st_bull and not base_bull) or (not st_bull and base_bull):
            scores.append((-0.5, f"🔴 Neg ST: ST{st_emoji}"))
            logger.debug(f"[{symbol}] SuperTrend trend uyumsuzluğu: -0.5 puan (ST: {st_trend}, Pattern: {price_direction})")

        return scores

    def _score_daily_bias(self, symbol: str, daily_bias: Optional[int], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        Daily Bias puanlaması

        Args:
            symbol: Sembol (örn: "BTCUSDT")
            daily_bias: Günlük bias değeri (1: bullish, -1: bearish, 0: nötr)
            trade_direction: Bull veya bear pattern varlığına göre belirlenen işlem yönü

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        if daily_bias is None or not trade_direction:
            logger.debug(f"[{symbol}] Daily Bias skoru hesaplanamadı: daily_bias={daily_bias}, trade_direction={trade_direction}")
            return scores

        db_score = 0.5  # Daily Bias puan değeri

        logger.info(f"[{symbol}] Daily Bias Değeri: {daily_bias}, Trade Direction: {trade_direction}")

        # Pozitif puanlama - yön uyumlu
        if (daily_bias == 1 and trade_direction == "bull") or (daily_bias == -1 and trade_direction == "bear"):
            db_emoji = "🟢" if daily_bias == 1 else "🔴"
            db_label = "DB+" if daily_bias == 1 else "DB-"
            scores.append((db_score, f"{db_emoji} {db_label}"))
            logger.info(f"[{symbol}] Daily Bias uyumluluğu: +{db_score:.1f} puan eklendi")

        # Negatif puanlama - yön uyumsuz
        elif (daily_bias == 1 and trade_direction == "bear") or (daily_bias == -1 and trade_direction == "bull"):
            db_emoji = "🟢" if daily_bias == 1 else "🔴"
            db_label = "DB+" if daily_bias == 1 else "DB-"
            scores.append((-db_score, f"{db_emoji} {db_label}"))
            logger.info(f"[{symbol}] Daily Bias uyumsuzluğu: -{db_score:.1f} puan eklendi")

        return scores

    def _score_fibonacci(self, symbol: str, timeframe: str, fib_data: Dict[str, Any], stats: Dict[str, Any], trade_direction: Optional[str] = None) -> List[Tuple[float, str]]:
        """
        Fibonacci analiz sonuçlarına göre puanlama yapar.
        Sadece günlük (D) zaman diliminde çalışır.
        Fiyat yalnızca bir Fibonacci bölgesinde puanlanır (öncelik sırasına göre).

        Desteklenen Fibonacci seviyeleri:
        - -0.618 (%-61.8)
        - -0.382 (%-38.2)
        - -0.214 (%-21.4)
        - -0.114 (%-11.4)
        - 0.114 (%11.4)
        - 0.214 (%21.4)
        - 0.382 (%38.2)
        - 0.5 (%50) - EQ seviyesi
        - 0.618 (%61.8)
        - 0.705 (%70.5) - OTE (Optimal Trade Entry) seviyesi
        - 0.786 (%78.6)
        - 0.886 (%88.6)
        - 1.114 (%111.4)
        - 1.214 (%121.4)
        - 1.618 (%161.8)

        Args:
            symbol (str): Kripto para sembolü
            timeframe (str): Zaman dilimi
            fib_data (Dict): Fibonacci analiz sonuçları (all_timeframe_data[symbol]['D']['fib_levels'])
            stats (Dict): Fiyat istatistikleri
            trade_direction (Optional[str]): İşlem yönü ('bull' veya 'bear')

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        # 4 saatlik (240) zaman diliminde de çalışabilir, ancak günlük Fibonacci verilerini kullanır
        if timeframe != 'D' and timeframe != '240':
            logger.debug(f"[{symbol}] Fibonacci puanlaması sadece günlük (D) veya 4 saatlik (240) zaman diliminde yapılır. Mevcut: {timeframe}")
            return scores

        # 4 saatlik zaman diliminde çalışırken, günlük Fibonacci verilerini kullanıyoruz
        if timeframe == '240':
            logger.debug(f"[{symbol}] 4 saatlik zaman diliminde günlük Fibonacci verileri kullanılıyor. fib_data: {bool(fib_data)}")

        if not fib_data or not stats:
            logger.warning(f"[{symbol}] Fibonacci puanlaması için fib_data veya stats verisi eksik. fib_data: {bool(fib_data)}, stats: {bool(stats)}")
            return scores

        last_price = stats.get("last_price")
        if not last_price:
            logger.warning(f"[{symbol}] Fibonacci puanlaması için last_price verisi eksik.")
            return scores

        # Timeframe etiketini belirle
        tf_label = "Daily"  # Günlük için sabit etiket

        # Fibonacci seviyelerine göre puanlama
        levels = fib_data.get('levels', {})
        zones = fib_data.get('zones', {})
        is_uptrend = fib_data.get('is_uptrend')
        last_swing_type = fib_data.get('last_swing_type')

        # Fiyatın hangi Fibonacci bölgesinde olduğunu belirle (öncelik sırasına göre)
        fib_zone_found = False

        # Fibonacci seviyelerini tanımla (resimde görünen tüm seviyeler, eksi olanlar dahil)
        fib_levels = [
            (-1.618, "-1.618"),  # %-161.8
            (-1.214, "-1.214"),  # %-121.4
            (-1.114, "-1.114"),  # %-111.4
            (-1.0, "-1.0"),      # %-100.0
            (-0.886, "-0.886"),  # %-88.6
            (-0.786, "-0.786"),  # %-78.6
            (-0.705, "-0.705"),  # %-70.5
            (-0.66, "-0.66"),    # %-66.0
            (-0.618, "-0.618"),  # %-61.8
            (-0.5, "-0.5"),      # %-50.0
            (-0.382, "-0.382"),  # %-38.2
            (-0.34, "-0.34"),       # %-34.0
            (-0.214, "-0.214"),  # %-21.4)
            (-0.114, "-0.114"),  # %-11.4
            (0.0, "0.0"),        # %0.0
            (0.114, "0.114"),    # %11.4
            (0.214, "0.214"),    # %21.4
            (0.34, "0.34"),      # %34.0
            (0.382, "0.382"),    # %38.2
            (0.5, "0.5"),        # %50 (EQ)
            (0.618, "0.618"),    # %61.8
            (0.66, "0.66"),      # %66.0
            (0.705, "0.705"),    # %70.5 (OTE - Optimal Trade Entry)
            (0.786, "0.786"),    # %78.6
            (0.886, "0.886"),    # %88.6
            (1.0, "1.0"),        # %100.0
            (1.114, "1.114"),    # %111.4
            (1.214, "1.214"),    # %121.4
            (1.618, "1.618")     # %161.8
        ]

        # Bölgeleri ve seviyeleri kontrol et

        # Tüm bölgeleri tanımla
        golden_spot1 = zones.get('golden_spot1')  # 0.34 - 0.382
        golden_spot2 = zones.get('golden_spot2')  # 0.618 - 0.66
        ote_zone = zones.get('ote')  # 0.705 - 0.786
        ext_zone1 = zones.get('ext_zone1')  # 0.114 - 0.214
        ext_zone2 = zones.get('ext_zone2')  # 0.786 - 0.886
        ext_zone3 = zones.get('ext_zone3')  # 1.114 - 1.214
        neg_ext_zone = zones.get('neg_ext_zone')  # -0.114 - -0.214

        # Puanlama sistemini yeniden düzenle:
        # 1. Öncelik: Bölgeler (Golden Spot 1, Golden Spot 2, OTE, Ext Zone 1, Ext Zone 2, Ext Zone 3, Neg Ext Zone)
        # 2. Öncelik: Seviyeler (EQ dahil tüm seviyeler, bölgelere denk gelen seviyeler hariç)

        # 1. Öncelik: Golden Spot 1 bölgesi (0.34 - 0.382 arası)
        if golden_spot1 and golden_spot1['start'] <= last_price <= golden_spot1['end']:
            gs_score = 1.5
            scores.append((gs_score, f"✅ Price in {tf_label} Fibonacci Golden Spot 1"))
            fib_zone_found = True
            logger.debug(f"[{symbol}] Fiyat {tf_label} Fibonacci Golden Spot 1 bölgesinde tespit edildi.")

        # 2. Öncelik: Golden Spot 2 bölgesi (0.618 - 0.66 arası)
        elif not fib_zone_found and golden_spot2 and golden_spot2['start'] <= last_price <= golden_spot2['end']:
            gs_score = 1.5
            scores.append((gs_score, f"✅ Price in {tf_label} Fibonacci Golden Spot 2"))
            fib_zone_found = True
            logger.debug(f"[{symbol}] Fiyat {tf_label} Fibonacci Golden Spot 2 bölgesinde tespit edildi.")

        # 3. Öncelik: OTE bölgesi (0.705 - 0.786 arası)
        elif not fib_zone_found and ote_zone and ote_zone['start'] <= last_price <= ote_zone['end']:
            ote_score = 1.5
            scores.append((ote_score, f"✅ Price in {tf_label} Fibonacci OTE"))
            fib_zone_found = True
            logger.debug(f"[{symbol}] Fiyat {tf_label} Fibonacci OTE bölgesinde tespit edildi.")

        # 4. Öncelik: Extend Zone 1 (0.114 - 0.214 arası) - Sadece bearish patternlerde puan alır
        elif not fib_zone_found and ext_zone1 and ext_zone1['start'] <= last_price <= ext_zone1['end']:
            # trade_direction kontrolü ekle - sadece bearish patternlerde puan ver
            if trade_direction != "bull":  # bull değilse (bear veya None ise)
                ext_score = 1.5
                scores.append((ext_score, f"✅ Price in {tf_label} Fibonacci Extend Zone 1"))
                fib_zone_found = True
                logger.debug(f"[{symbol}] Fiyat {tf_label} Fibonacci Extend Zone 1 bölgesinde tespit edildi (Bearish pattern).")
            else:
                logger.debug(f"[{symbol}] Fiyat {tf_label} Fibonacci Extend Zone 1 bölgesinde tespit edildi, ancak bullish pattern olduğu için puan verilmedi.")

        # 5. Öncelik: Extend Zone 2 (0.786 - 0.886 arası) - Sadece bullish patternlerde puan alır
        elif not fib_zone_found and ext_zone2 and ext_zone2['start'] <= last_price <= ext_zone2['end']:
            # trade_direction kontrolü ekle - sadece bullish patternlerde puan ver
            if trade_direction != "bear":  # bear değilse (bull veya None ise)
                ext_score = 1.5
                scores.append((ext_score, f"✅ Price in {tf_label} Fibonacci Extend Zone 2"))
                fib_zone_found = True
                logger.debug(f"[{symbol}] Fiyat {tf_label} Fibonacci Extend Zone 2 bölgesinde tespit edildi (Bullish pattern).")
            else:
                logger.debug(f"[{symbol}] Fiyat {tf_label} Fibonacci Extend Zone 2 bölgesinde tespit edildi, ancak bearish pattern olduğu için puan verilmedi.")

        # 6. Öncelik: Extend Zone 3 (1.114 - 1.214 arası) - Sadece bullish patternlerde puan alır
        elif not fib_zone_found and ext_zone3 and ext_zone3['start'] <= last_price <= ext_zone3['end']:
            # trade_direction kontrolü ekle - sadece bullish patternlerde puan ver
            if trade_direction != "bear":  # bear değilse (bull veya None ise)
                ext_score = 1.5
                scores.append((ext_score, f"✅ Price in {tf_label} Fibonacci Extend Zone 3"))
                fib_zone_found = True
                logger.debug(f"[{symbol}] Fiyat {tf_label} Fibonacci Extend Zone 3 bölgesinde tespit edildi (Bullish pattern).")
            else:
                logger.debug(f"[{symbol}] Fiyat {tf_label} Fibonacci Extend Zone 3 bölgesinde tespit edildi, ancak bearish pattern olduğu için puan verilmedi.")

        # 7. Öncelik: Negatif Extend Zone (-0.114 - -0.214 arası) - Sadece bearish patternlerde puan alır
        elif not fib_zone_found and neg_ext_zone and neg_ext_zone['start'] <= last_price <= neg_ext_zone['end']:
            # trade_direction kontrolü ekle - sadece bearish patternlerde puan ver
            if trade_direction != "bull":  # bull değilse (bear veya None ise)
                neg_ext_score = 1.5
                scores.append((neg_ext_score, f"✅ Price in {tf_label} Fibonacci Negative Extend Zone"))
                fib_zone_found = True
                logger.debug(f"[{symbol}] Fiyat {tf_label} Fibonacci Negative Extend Zone bölgesinde tespit edildi (Bearish pattern).")
            else:
                logger.debug(f"[{symbol}] Fiyat {tf_label} Fibonacci Negative Extend Zone bölgesinde tespit edildi, ancak bullish pattern olduğu için puan verilmedi.")

        # 4. Öncelik: Diğer Fibonacci seviyeleri (EQ dahil, bölgelere denk gelen seviyeler hariç)
        elif not fib_zone_found:
            # Bölgelere denk gelen seviyeleri ve puanlanmayacak seviyeleri (0 ve 1) tanımla
            zone_levels = [0.0, 1.0, 0.114, 0.214, 0.34, 0.382, 0.618, 0.66, 0.705, 0.786, 0.886, 1.114, 1.214, -0.114, -0.214]

            # Fiyatın her bir Fibonacci seviyesine yakınlığını kontrol et
            for level_value, level_name in fib_levels:
                # Bölgelere denk gelen seviyeleri atla
                if level_value in zone_levels:
                    continue

                if level_value in levels:
                    level_price = levels[level_value]['price']
                    # Sabit fiyat toleransı kullan (özellikle negatif seviyeler için)
                    # Fiyatın %0.5'i kadar mutlak tolerans (1.0% yerine 0.5% kullanılıyor)
                    price_tolerance = last_price * 0.005
                    if abs(last_price - level_price) <= price_tolerance and not fib_zone_found:
                        # Tüm seviyeler için 1.0 puan ver (EQ dahil)
                        # Negatif seviyeler için özel mesaj
                        if level_value < 0:
                            scores.append((1.0, f"✅ {tf_label} Fibonacci {level_name} (Negative)"))
                            logger.info(f"[{symbol}] Fiyat {tf_label} Fibonacci {level_name} (Negatif) seviyesinde tespit edildi. Fiyat: {last_price:.4f}, Seviye: {level_price:.4f}, Tolerans: {price_tolerance:.4f}")
                        else:
                            scores.append((1.0, f"✅ {tf_label} Fibonacci {level_name}"))
                            logger.info(f"[{symbol}] Fiyat {tf_label} Fibonacci {level_name} seviyesinde tespit edildi. Fiyat: {last_price:.4f}, Seviye: {level_price:.4f}, Tolerans: {price_tolerance:.4f}")
                        break  # İlk bulunan seviyede dur

        # Premium/Discount bilgisini ekle (varsa)
        if fib_zone_found and symbol in self.scores and "pd_zones" in self.scores[symbol]:
            pd_zones = self.scores[symbol]["pd_zones"]
            zone_name = "Premium" if pd_zones.get('is_premium', False) else "Discount"
            logger.debug(f"[{symbol}] Fiyat {zone_name} bölgesinde (EQ: {pd_zones['eq_level']:.4f})")

        # Son swing tipine göre ek puanlama (bu, bölge tespitinden bağımsız olarak yapılabilir)
        if last_swing_type and is_uptrend is not None:
            # Bullish Fibonacci Retracement Completion:
            # Yükselen bir trendde, fiyat bir geri çekilme yapıp HL (Higher Low) oluşturduğunda
            if last_swing_type == 'HL':
                scores.append((1.0, f"✅ Bullish Fib Ret Comp ({last_swing_type})"))
                logger.debug(f"[{symbol}] Bullish Fibonacci Retracement tamamlandı: {last_swing_type}")
            # Bearish Fibonacci Retracement Completion:
            # Düşen bir trendde, fiyat bir geri çekilme yapıp LH (Lower High) oluşturduğunda
            elif last_swing_type == 'LH':
                scores.append((1.0, f"✅ Bearish Fib Ret Comp ({last_swing_type})"))
                logger.debug(f"[{symbol}] Bearish Fibonacci Retracement tamamlandı: {last_swing_type}")

        return scores

    def _calculate_other_timeframe_scores(self, symbol: str, all_timeframe_data: Dict[str, Dict[str, Dict[str, Any]]], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        Diğer zaman dilimlerindeki verilere göre puanlama yapar.
        Ana `trade_direction`'ı kullanarak FVG ve OB'ler için premium/discount değerlendirmesi yapar.

        Args:
            symbol (str): Kripto para sembolü
            all_timeframe_data (Dict): Tüm zaman dilimleri için analiz sonuçları
            trade_direction (Optional[str]): Ana işlem yönü ('bull' veya 'bear')

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        # Yön bilgisi yoksa puanlama yapma
        if trade_direction is None:
            logger.warning(f"[{symbol}] Diğer zaman dilimleri puanlaması yapılamadı: Yön bilgisi yok.")
            return scores

        # Symbol için tüm zaman dilimlerini kontrol et
        if symbol not in all_timeframe_data:
            logger.debug(f"[{symbol}] _calculate_other_timeframe_scores: all_timeframe_data içinde sembol bulunamadı")
            return scores

        timeframe_data = all_timeframe_data[symbol]

        # Log yapıları için
        if timeframe_data:
            logger.debug(f"[{symbol}] _calculate_other_timeframe_scores: Mevcut timeframes: {list(timeframe_data.keys())} | Ana Yön: {trade_direction}")
        else:
            logger.debug(f"[{symbol}] _calculate_other_timeframe_scores: Zaman dilimi verisi yok. | Ana Yön: {trade_direction}")
            return scores # Veri yoksa devam etme

        # Premium/Discount bilgilerini al
        pd_zones = self.scores.get(symbol, {}).get("pd_zones")
        if not pd_zones:
            logger.debug(f"[{symbol}] Premium/Discount bilgisi bulunamadı")

        # --- Diğer Zaman Dilimleri için Puanlama ---
        # Tüm diğer zaman dilimlerini kontrol et
        for timeframe in self.other_timeframes:
            if timeframe in timeframe_data:
                tf_data = timeframe_data[timeframe]

                # Pattern puanlaması
                if 'patterns' in tf_data and tf_data['patterns']:
                    pattern_scores = self._score_patterns_other_tf(symbol, timeframe, tf_data['patterns'], trade_direction)
                    scores.extend(pattern_scores)

                # BOS puanlaması
                if 'bos_results' in tf_data and tf_data['bos_results']:
                    bos_scores = self._score_bos_other_tf(symbol, timeframe, tf_data['bos_results'], trade_direction)
                    scores.extend(bos_scores)

                # SFP puanlaması
                if 'sfps' in tf_data and tf_data['sfps']:
                    sfp_scores = self._score_sfp(symbol, timeframe, tf_data['sfps'], trade_direction)
                    scores.extend(sfp_scores)

                # Divergence puanlaması (720 ve D zaman dilimlerinde atla)
                if 'divergences' in tf_data and tf_data['divergences'] and timeframe != "720" and timeframe != "D":
                    divergence_scores = self._score_divergence(symbol, timeframe, tf_data['divergences'], trade_direction)
                    scores.extend(divergence_scores)
                elif 'divergences' in tf_data and tf_data['divergences'] and (timeframe == "720" or timeframe == "D"):
                    logger.info(f"[{symbol}] {timeframe} zaman diliminde divergence puanlaması atlanıyor (diğer timeframe).")

                # S/R Flip puanlaması (sadece günlük zaman dilimi için)
                if timeframe == "D" and 'sr_flip_data' in tf_data and tf_data['sr_flip_data']:
                    sr_flip_scores = self._score_sr_flips(tf_data['sr_flip_data'], trade_direction)
                    scores.extend(sr_flip_scores)
                    logger.debug(f"[{symbol}] Günlük S/R Flip puanlaması eklendi: {len(sr_flip_scores)} puan")

        # Ana timeframe '240' için FVG/OB puanlaması kaldırıldı
        # Çünkü ana timeframe zaten _calculate_main_confirmation_scores içinde puanlanıyor
        # Bu çakışmayı önlemek için kaldırıldı

        return scores

    def generate_score_report(self, all_timeframe_data: Dict[str, Dict[str, Dict]], min_score_threshold: float = 4.0) -> Dict[str, str]:
        """
        Potansiyel sinyaller için, sadece kendisine verilen veriyi kullanarak bir rapor metni oluşturur.
        (self.scores gibi dış durumlara bağımlı değildir.)
        """
        symbol_reports = {}
        for symbol, timeframes_data in all_timeframe_data.items():
            report_lines = []
            for timeframe, data in timeframes_data.items():
                if timeframe != self.main_timeframe:
                    continue

                # Veri kaynağı: Argüman olarak gelen 'data' (score_result içerir)
                score_result = data.get("score_result")
                if not score_result:
                    continue

                net_score = score_result.get('net_score', 0.0)
                if net_score < min_score_threshold:
                    continue

                # DÜZELTME: trade_direction veya direction kullan, öncelik trade_direction
                direction = score_result.get('trade_direction')
                if direction is None:
                    direction = score_result.get('direction', 'N/A')
                direction_emoji = "🔼" if direction == 'bull' else "🔻"

                # Veriyi her zaman score_result'tan al, self.scores'tan değil.
                entry = score_result.get('entry_price')
                sl = score_result.get('sl_price')
                tp1 = score_result.get('tp1_price')
                tp2 = score_result.get('tp2_price')
                tp3 = score_result.get('tp3_price')
                
                if entry is None or sl is None:
                    logger.debug(f"[{symbol}] Rapor oluşturma atlandı: Giriş veya SL fiyatı eksik.")
                    continue

                # SL ve TP yüzdelerini doğru hesapla
                if entry and sl:
                    if direction == 'bull':
                        sl_pct = ((entry - sl) / entry) * 100  # Bullish: entry'den düşük SL
                    else:
                        sl_pct = ((sl - entry) / entry) * 100  # Bearish: entry'den yüksek SL
                else:
                    sl_pct = 0.0

                # Timeframe formatı düzelt
                tf_display = "4h" if timeframe == "240" else timeframe

                # Yön metni düzelt
                direction_text = "LONG" if direction == 'bull' else "SHORT" if direction == 'bear' else "UNKNOWN"
                report_lines.append(f"🎯 {symbol} | {tf_display} | 📊 Net Score: {net_score:.1f} | {direction_emoji} {direction_text} |")
                report_lines.append(f" ---------------------------------")

                base_score = score_result.get('base_score', 0)
                conf_score = score_result.get('confirmation_score', 0)
                neg_score = score_result.get('negative_score', 0)
                report_lines.append(f"📊 Base: {base_score:.1f}, ")
                report_lines.append(f"      Conf. {conf_score:.1f}, ")
                report_lines.append(f"      Neg. {neg_score:.1f}")
                report_lines.append("")

                # R:R hesaplaması düzelt
                if tp1 and entry and sl:
                    if direction == 'bull':
                        risk = entry - sl
                        reward = tp1 - entry
                    else:
                        risk = sl - entry  
                        reward = entry - tp1
                    
                    risk_reward_ratio = reward / risk if risk > 0 else 0
                else:
                    risk_reward_ratio = 0

                report_lines.append(f"💰 Trade Levels: R:R: {risk_reward_ratio:.2f}")
                report_lines.append(f"----------------------------")
                
                # Volatilite seviyesi ve strateji bilgilerini ekle
                volatility_level = score_result.get("volatility_level", "unknown")
                if volatility_level != "unknown":
                    report_lines.append(f"      🌊 Volatilite: {volatility_level.upper()}")
                
                # Fibonacci seviyesi bilgisini ekle
                fib_level = score_result.get("fib_level")
                if fib_level:
                    report_lines.append(f"      📏 Fib Seviyesi: {fib_level}")
                
                # Kullanılan strateji bilgisini ekle
                strategy_used = score_result.get("strategy_used", "")
                if strategy_used:
                    if "fvg_fibonacci" in strategy_used.lower():
                        report_lines.append(f"      📈 Strateji: FVG+Fib")
                    elif "fibonacci" in strategy_used.lower():
                        if strategy_used == "fibonacci_distant":
                            report_lines.append(f"      📈 Strateji: Fib (⚠️ Uzak)")
                        else:
                            report_lines.append(f"      📈 Strateji: Fib")
                
                report_lines.append(f"      Entry: {self.format_price(entry)} ")
                report_lines.append(f"      SL: {self.format_price(sl)} ({sl_pct:.2f}%) ")
                
                # TP yüzdelerini doğru hesapla
                if tp1 and entry:
                    if direction == 'bull':
                        tp1_pct = ((tp1 - entry) / entry) * 100
                    else:
                        tp1_pct = ((entry - tp1) / entry) * 100
                    report_lines.append(f"      TP1: {self.format_price(tp1)} ({tp1_pct:.2f}%) ")
                
                if tp2 and entry:
                    if direction == 'bull':
                        tp2_pct = ((tp2 - entry) / entry) * 100
                    else:
                        tp2_pct = ((entry - tp2) / entry) * 100
                    report_lines.append(f"      TP2: {self.format_price(tp2)} ({tp2_pct:.2f}%) ")
                
                if tp3 and entry:
                    if direction == 'bull':
                        tp3_pct = ((tp3 - entry) / entry) * 100
                    else:
                        tp3_pct = ((entry - tp3) / entry) * 100
                    report_lines.append(f"      TP3: {self.format_price(tp3)} ({tp3_pct:.2f}%) ")
                
                report_lines.append("")
                
                # Base Signals detaylarını ekle
                base_details = score_result.get('base_details', [])
                if base_details:
                    report_lines.append(f"🔍 Base Signals: ")
                    report_lines.append(f"----------------------------")
                    report_lines.append("     - " + ", ".join([detail[1] if isinstance(detail, (list, tuple)) and len(detail) > 1 else str(detail) for detail in base_details]) + " -")
                    report_lines.append("")

                # Premium/Discount bilgisini ekle
                pd_label = score_result.get("pd_label", "N/A")
                eq_level = score_result.get('eq_level', 'N/A')
                if eq_level != 'N/A' and isinstance(eq_level, (int, float)):
                    eq_level_formatted = self.format_price(eq_level)
                    report_lines.append(f"📍 {pd_label} (EQ: {eq_level_formatted}) ")
                else:
                    report_lines.append(f"📍 {pd_label} ")

                # Order Blocks bilgisini ekle
                bull_ob = "N/A"
                bear_ob = "N/A"
                if "bull_ob" in score_result:
                    bull_ob = "Yes" if score_result["bull_ob"] else "No"
                if "bear_ob" in score_result:
                    bear_ob = "Yes" if score_result["bear_ob"] else "No"

                report_lines.append("----------------------------")
                report_lines.append(f"    OBs: Bull: {bull_ob}, ")
                report_lines.append(f"    Bear: {bear_ob}")
                report_lines.append(" ---------------------------")

                # Confirmations ekle
                confirmation_details = score_result.get('confirmation_details', [])
                if confirmation_details:
                    report_lines.append("")
                    report_lines.append("✅ Confirmations:")
                    for detail in confirmation_details:
                        if isinstance(detail, (list, tuple)) and len(detail) > 1:
                            report_lines.append(f"  {detail[1]}")
                        else:
                            report_lines.append(f"  {str(detail)}")

                # Negatives/Conflicts ekle
                negative_details = score_result.get('negative_details', [])
                if negative_details:
                    report_lines.append("")
                    report_lines.append("⛔ Conflicts:")
                    for detail in negative_details:
                        if isinstance(detail, (list, tuple)) and len(detail) > 1:
                            report_lines.append(f"  {detail[1]}")
                        else:
                            report_lines.append(f"  {str(detail)}")

            if report_lines:
                symbol_reports[symbol] = "\n".join(report_lines)

        return symbol_reports

    def _calculate_main_confirmation_scores(self, symbol: str, timeframe: str, stats: Optional[Dict[str, Any]], daily_bias: Optional[int], supertrend: Optional[Dict[str, Any]], fvgs: Optional[List[Dict[str, Any]]], fib_data: Optional[Dict[str, Any]], timeframe_levels: Optional[Dict[str, Any]], order_blocks: Optional[Dict[str, Any]], naked_pocs: Optional[List[Dict[str, Any]]], sfps: Optional[List[Dict[str, Any]]], divergences: Optional[List[Dict[str, Any]]], bos_results: Optional[Dict[str, Any]], trade_direction: Optional[str]):
        """Ana zaman dilimi için teyit ve negatif skorları hesaplar."""

        if symbol not in self.scores or "direction" not in self.scores[symbol]:
             logger.warning(f"[{symbol}] Ana zaman dilimi teyit skorları hesaplanamadı: Yön bilgisi eksik.")
             return

        # Parametre olarak gelen trade_direction değerini kullan, eğer None ise scores'dan al
        if trade_direction is None:
            trade_direction = self.scores[symbol]["direction"]
            logger.debug(f"[{symbol}] Parametre olarak gelen trade_direction None, scores'dan alındı: {trade_direction}")

        # Yön bilgisi hala None ise işlem yapma
        if trade_direction is None:
            logger.warning(f"[{symbol}] Ana zaman dilimi teyit skorları hesaplanamadı: Geçerli yön bilgisi yok.")
            return

        # Daily Bias kullanımı hakkında bilgi loglayalım
        if daily_bias is not None:
            logger.info(f"[{symbol}] Ana zaman dilimi hesaplamasında Daily Bias değeri: {daily_bias}, Yön: {trade_direction}")

        # Hesaplanan skorları toplayacağımız geçici listeler
        all_scores = []

        # İlgili _score metotlarını çağır (trade_direction ile)
        all_scores.extend(self._score_ema(symbol, timeframe, stats, trade_direction))
        all_scores.extend(self._score_vwap(symbol, timeframe, stats, trade_direction))
        all_scores.extend(self._score_funding_rate(symbol, timeframe, stats, trade_direction))
        all_scores.extend(self._score_daily_bias(symbol, daily_bias, trade_direction))
        all_scores.extend(self._score_supertrend(symbol, timeframe, supertrend, trade_direction))

        # Divergence puanlaması (720 ve D zaman dilimlerinde atla)
        if timeframe != "720" and timeframe != "D":
            all_scores.extend(self._score_divergence(symbol, timeframe, divergences, trade_direction))
        else:
            logger.info(f"[{symbol}] {timeframe} zaman diliminde divergence puanlaması atlanıyor (ana timeframe).")

        all_scores.extend(self._score_sfp(symbol, timeframe, sfps, trade_direction))

        # Ana zaman dilimi için FVG, Fib, Order Block, Naked POC, EQ Levels puanlaması eklenmiştir
        all_scores.extend(self._score_fvg(symbol, timeframe, fvgs, stats, trade_direction))
        all_scores.extend(self._score_fibonacci(symbol, timeframe, fib_data, stats, trade_direction))  # trade_direction parametresi eklendi
        all_scores.extend(self._score_eq_levels(symbol, timeframe, timeframe_levels, stats, trade_direction))
        all_scores.extend(self._score_order_blocks(symbol, timeframe, order_blocks, stats, trade_direction))
        all_scores.extend(self._score_naked_poc(symbol, timeframe, naked_pocs, stats))

        # BOS yöntemini de ekleyelim (diğer timeframe'ler için zaten yapılıyor)
        all_scores.extend(self._score_bos_other_tf(symbol, timeframe, bos_results, trade_direction))

        # S/R Flip puanlaması kaldırıldı - _calculate_other_timeframe_scores içinde yapılacak
        # Çünkü S/R Flip günlük (D) zaman dilimi verilerini kullanıyor

        # Toplanan skorları işle
        for score, detail in all_scores:
            if score > 0:
                self.scores[symbol]["confirmation_score"] = self.scores[symbol].get("confirmation_score", 0.0) + score
                # Tekrarlanan pozitif mesajları önle
                if detail not in self.scores[symbol]["confirmation_details"]:
                    self.scores[symbol]["confirmation_details"].append(detail)
            elif score < 0:
                self.scores[symbol]["negative_score"] = self.scores[symbol].get("negative_score", 0.0) + score
                # Tekrarlanan negatif mesajları önle
                if detail not in self.scores[symbol]["negative_details"]:
                    self.scores[symbol]["negative_details"].append(detail)

    def _score_fvg(self, symbol: str, timeframe: str, fvgs: Optional[List[Dict[str, Any]]], stats: Optional[Dict[str, Any]], trade_direction: Optional[str] = None) -> List[Tuple[float, str]]:
        """
        FVG'lere göre puanlama yapar.
        Günlük (D) timeframe için sadece EQ seviyesi dikkate alınır.
        Diğer timeframeler için FVG içi, EQ ve premium/discount durumları kontrol edilir.

        Args:
            symbol (str): Kripto para sembolü
            timeframe (str): Zaman dilimi ('D', '240' vb.)
            fvgs (List[Dict]): FVG analiz sonuçları
            stats (Dict): Fiyat bilgileri
            trade_direction (str): İşlem yönü ('bull' veya 'bear')

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []
        if not fvgs or not stats:
            return scores

        last_price = stats.get("last_price")
        if not last_price:
            return scores

        # Son 3 FVG'yi puanlamada kullan (son oluşanlar daha önemli)
        recent_fvgs = fvgs[-3:] if len(fvgs) > 3 else fvgs

        for fvg in recent_fvgs:
            fvg_type = fvg['type']
            fvg_top = fvg['top']
            fvg_bottom = fvg['bottom']
            fvg_eq = fvg['eq']
            fvg_filled = fvg['filled']

            # FVG tamamen doldurulmuşsa puanlamaya etkisi daha az olmalı
            weight = 0.5 if fvg_filled else 1.0

            # Zaman dilimi etiketini ekle (4H veya D)
            tf_label = self._get_timeframe_label(timeframe)
            fvg_label = f"{tf_label} FVG ({fvg_type[:4]})" # Örn: "D FVG (bull)" veya "4H FVG (bull)"


            # --- GÜNLÜK ZAMAN DİLİMİ ÖZEL KONTROLÜ ---
            if timeframe == 'D':
                # Sadece EQ kontrolü yap
                eq_band = abs(fvg_top - fvg_bottom) * 0.05
                if abs(last_price - fvg_eq) <= eq_band:
                    # Günlük EQ temasını daha önemli yapalım (örneğin 2.0 puan)
                    scores.append((2.0 * weight, f"✅✅ Daily FVG EQ"))
                # Günlük için başka kontrol yapma, döngünün başına dön
                continue
            # --- Günlük Kontrol Sonu ---


            # --- DİĞER ZAMAN DİLİMLERİ İÇİN KONTROLLER ---
            # FVG tipi ile işlem yönü uyumlu mu kontrol et
            is_fvg_aligned = False
            if trade_direction:
                is_fvg_aligned = (fvg_type == "bullish" and trade_direction == "bull") or (fvg_type == "bearish" and trade_direction == "bear")
                logger.debug(f"[{symbol}] FVG tipi ({fvg_type}) ile işlem yönü ({trade_direction}) uyumluluğu: {is_fvg_aligned}")

            # 1. Fiyat FVG içinde mi? (Sadece işlem yönü ile uyumlu FVG'ler için)
            if fvg_bottom <= last_price <= fvg_top:
                # İşlem yönü yoksa veya FVG tipi ile işlem yönü uyumlusa puan ver
                if trade_direction is None or is_fvg_aligned:
                    scores.append((1.0 * weight, f"✅ Price in {fvg_label}"))
                    logger.debug(f"[{symbol}] Fiyat {fvg_label} içinde, puan: +{1.0 * weight:.1f}")
                else:
                    logger.debug(f"[{symbol}] Fiyat {fvg_label} içinde, ancak işlem yönü ({trade_direction}) ile uyumsuz olduğu için puan verilmedi.")

            # 2. Fiyat FVG'nin EQ bölgesine yakın mı? (Sadece işlem yönü ile uyumlu FVG'ler için)
            eq_band = abs(fvg_top - fvg_bottom) * 0.05
            if abs(last_price - fvg_eq) <= eq_band:
                # İşlem yönü yoksa veya FVG tipi ile işlem yönü uyumlusa puan ver
                if trade_direction is None or is_fvg_aligned:
                    scores.append((1.5 * weight, f"✅✅ Price at {fvg_label} EQ"))
                    logger.debug(f"[{symbol}] Fiyat {fvg_label} EQ noktasında, puan: +{1.5 * weight:.1f}")
                else:
                    logger.debug(f"[{symbol}] Fiyat {fvg_label} EQ noktasında, ancak işlem yönü ({trade_direction}) ile uyumsuz olduğu için puan verilmedi.")

            # Premium/Discount puanlaması artık yeni sistem (score_structure_premium_discount) tarafından yapılıyor
            # Eski Premium/Discount puanlaması kaldırıldı - çakışmayı önlemek için
            # --- Diğer Zaman Dilimleri Kontrolleri Sonu ---

        return scores

    def _score_sr_flips(self, sr_flip_data: Dict[str, Any], trade_direction: Optional[str]) -> List[Tuple[float, str]]:
        """
        S/R Flip olaylarına puan verir.

        S/R Flip, piyasanın "hafızasını" ve önceki engellerin artık birer basamak
        olduğunu gösteren güçlü bir teyit mekanizmasıdır.

        Args:
            sr_flip_data (Dict): S/R Flip analiz sonuçları
                                Format: {'bullish_flips': [fiyat_seviyesi], 'bearish_flips': [fiyat_seviyesi]}
            trade_direction (Optional[str]): İşlem yönü ('bull' veya 'bear')

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []
        weight = 1.5  # S/R Flip güçlü bir teyit olduğu için yüksek puan

        if not sr_flip_data:
            return scores

        bullish_flips = sr_flip_data.get('bullish_flips', [])
        bearish_flips = sr_flip_data.get('bearish_flips', [])

        # Bullish S/R Flip puanlaması
        if trade_direction == "bull" and bullish_flips:
            level = bullish_flips[0]  # İlk seviyeyi al
            scores.append((weight, f"💎 Bullish S/R Flip at {level:.4f}"))
            logger.debug(f"Puan: +{weight:.1f} (Bullish S/R Flip)")

        # Bearish S/R Flip puanlaması
        if trade_direction == "bear" and bearish_flips:
            level = bearish_flips[0]  # İlk seviyeyi al
            scores.append((weight, f"💎 Bearish S/R Flip at {level:.4f}"))
            logger.debug(f"Puan: +{weight:.1f} (Bearish S/R Flip)")

        return scores


