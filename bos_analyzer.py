"""
Break of Structure (BOS) <PERSON><PERSON><PERSON>.
Piyasa yapısının kırılmasını tespit eder.
"""

import pandas as pd
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
from loguru import logger
from utils import format_price_standard


class BOSAnalyzer:
    """
    Break of Structure (BOS) analizi için sınıf.
    
    BOS (Piyasa Yapısı Kırılması), fiyat hareketlerinde trend değişimi işareti olabilecek
    belirli swing dizilimlerini tespit eder.
    
    Bullish BOS: LH,LL,HH dizilimi (aşağı trendin kırılıp yukarı trende dönüşme sinyali)
    Bearish BOS: HL,HH,LL dizilimi (yukarı trendin kırılıp aşağı trende dönüşme sinyali)
    """
    
    def __init__(self, match_depth: int = 3):
        """
        BOSAnalyzer sınıfını başlatır.
        
        Args:
            match_depth (int): BOS tespiti için kontrol edilecek son swing noktası sayısı.
                              Varsayılan: 3 (standart BOS desenleri 3 swing noktası kullanır)
        """
        self.match_depth = match_depth
        self.bull_bos_pattern = ["LH", "LL", "HH"]  # Boğa BOS deseni
        self.bear_bos_pattern = ["HL", "HH", "LL"]  # Ayı BOS deseni
        logger.info(f"BOSAnalyzer başlatıldı: match_depth={match_depth}")
    
    def detect_bos(self, swing_points: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Verilen swing noktalarından SADECE EN SON 3 noktayı kullanarak BOS (Break of Structure) desenlerini tespit eder.
        
        Args:
            swing_points (List[Dict]): PivotAnalyzer'dan gelen swing noktaları listesi.
        
        Returns:
            Dict[str, Any]: Tespit edilen BOS bilgilerini içeren sözlük.
        """
        result = {
            'bullish': None,
            'bearish': None
        }
        
        # Yalnızca 'type' değeri olan geçerli swing noktalarını filtrele
        valid_swing_points = [sp for sp in swing_points if sp.get('type')]
        
        # Yeterli swing noktası yoksa erken çık
        if len(valid_swing_points) < self.match_depth:
            logger.debug(f"BOS tespiti için yetersiz swing noktası: {len(valid_swing_points)}/{self.match_depth}")
            return result
        
        # Swing noktalarını indekse göre sırala (eski -> yeni)
        sorted_swing_points = sorted(valid_swing_points, key=lambda x: x.get('index', 0))
        
        # SADECE SON 3 SWING NOKTASINI AL
        last_swings = sorted_swing_points[-self.match_depth:]
        
        # Swing tiplerini kontrol et
        swing_types = [sp.get('type') for sp in last_swings]
        
        # Bullish BOS kontrolü (LH, LL, HH)
        if swing_types == self.bull_bos_pattern:
            hh_point = last_swings[-1]  # Son nokta (HH)
            ll_point = last_swings[-2]  # Ortadaki nokta (LL)
            
            bos_data = {
                'timestamp': hh_point.get('timestamp'),
                'price': hh_point.get('price'),
                'pattern': 'Bullish BOS',
                'swing_points': last_swings,
                'prev_low': ll_point.get('price'),
                'percent_change': ((hh_point.get('price') - ll_point.get('price')) / ll_point.get('price') * 100) 
                                  if ll_point.get('price') else None
            }
            result['bullish'] = bos_data
            logger.debug(f"Son 3 swing noktasında Bullish BOS tespit edildi")
        
        # Bearish BOS kontrolü (HL, HH, LL)
        elif swing_types == self.bear_bos_pattern:
            ll_point = last_swings[-1]  # Son nokta (LL)
            hh_point = last_swings[-2]  # Ortadaki nokta (HH)
            
            bos_data = {
                'timestamp': ll_point.get('timestamp'),
                'price': ll_point.get('price'),
                'pattern': 'Bearish BOS',
                'swing_points': last_swings,
                'prev_high': hh_point.get('price'),
                'percent_change': ((ll_point.get('price') - hh_point.get('price')) / hh_point.get('price') * 100) 
                                  if hh_point.get('price') else None
            }
            result['bearish'] = bos_data
            logger.debug(f"Son 3 swing noktasında Bearish BOS tespit edildi")
        else:
            logger.debug(f"Son 3 swing noktasında ({swing_types}) BOS deseni bulunamadı")
        
        return result
    
    def analyze_symbol(self, symbol: str, timeframe: str, swing_points: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Belirli bir sembol ve zaman dilimi için swing_points kullanarak BOS analizi yapar.
        
        Args:
            symbol (str): Analiz edilecek sembol (örn. "BTCUSDT")
            timeframe (str): Analiz edilecek zaman dilimi (örn. "60", "240", "D")
            swing_points (List[Dict]): PivotAnalyzer'dan gelen swing noktaları listesi
            
        Returns:
            Dict[str, Any]: Analiz sonuçlarını içeren sözlük.
        """
        
        # BOS analizi yap
        bos_results = self.detect_bos(swing_points)
        
        # Sembol ve zaman dilimi bilgilerini ekle
        bos_results['symbol'] = symbol
        bos_results['timeframe'] = timeframe
        
        # Sonuçları logla
        bull_bos = bos_results.get('bullish')
        bear_bos = bos_results.get('bearish')
        
        if bull_bos:
            timestamp_str = bull_bos.get('timestamp').strftime('%d-%m %H:%M') if bull_bos.get('timestamp') else 'N/A'
            logger.debug(f"[{symbol} {timeframe}] Bullish BOS tespit edildi: {format_price_standard(bull_bos.get('price'))}, {timestamp_str}")
        
        if bear_bos:
            timestamp_str = bear_bos.get('timestamp').strftime('%d-%m %H:%M') if bear_bos.get('timestamp') else 'N/A'
            logger.debug(f"[{symbol} {timeframe}] Bearish BOS tespit edildi: {format_price_standard(bear_bos.get('price'))}, {timestamp_str}")
        
        return bos_results
    
    def generate_summary_report(self, all_bos_results: Dict[str, Dict[str, Any]]) -> str:
        """
        Tüm BOS sonuçlarından özet bir rapor oluşturur.
        
        Args:
            all_bos_results (Dict[str, Dict[str, Any]]): Tüm BOS sonuçları. 
                                                        Anahtar formatı: {symbol}_{timeframe}
        
        Returns:
            str: Formatlanmış BOS özet raporu
        """
        if not all_bos_results:
            return "BOS sonucu bulunamadı."
        
        # Bullish ve Bearish BOS sayılarını hesapla
        bullish_bos = []
        bearish_bos = []
        
        for key, result in all_bos_results.items():
            parts = key.split('_')
            if len(parts) < 2:
                continue
                
            symbol = parts[0]
            timeframe = parts[1]
            
            # Bullish BOS
            if result.get('bullish'):
                bos_data = result['bullish']
                price = bos_data.get('price', 0)
                pct_change = bos_data.get('percent_change', 0)
                timestamp = bos_data.get('timestamp')
                time_str = timestamp.strftime('%d-%m %H:%M') if timestamp else 'N/A'
                
                bullish_bos.append({
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'price': price,
                    'pct_change': pct_change,
                    'time_str': time_str
                })
            
            # Bearish BOS
            if result.get('bearish'):
                bos_data = result['bearish']
                price = bos_data.get('price', 0)
                pct_change = bos_data.get('percent_change', 0)
                timestamp = bos_data.get('timestamp')
                time_str = timestamp.strftime('%d-%m %H:%M') if timestamp else 'N/A'
                
                bearish_bos.append({
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'price': price,
                    'pct_change': pct_change,
                    'time_str': time_str
                })
        
        # Özet raporu oluştur
        logger.info("===============================")
        logger.info("=== SON TESPIT EDILEN BOS'LAR ===")
        
        # Bullish BOS özeti
        if bullish_bos:
            logger.info(f"-- Bullish BOS ({len(bullish_bos)}) --")
            for bos in bullish_bos:
                symbol = bos['symbol']
                tf = bos['timeframe']
                price = format_price_standard(bos['price'])
                pct = bos['pct_change']
                time_str = bos['time_str']
                
                # Sembolün genişliği için boşluk ekle (10 karakter)
                symbol_padded = f"{symbol:<10}"
                
                logger.info(f"🟢 {symbol_padded} | {tf:<3} | Fiyat: {price} ({pct:+.1f}%) | {time_str}")
        
        # Bearish BOS özeti
        if bearish_bos:
            logger.info(f"-- Bearish BOS ({len(bearish_bos)}) --")
            for bos in bearish_bos:
                symbol = bos['symbol']
                tf = bos['timeframe']
                price = format_price_standard(bos['price'])
                pct = bos['pct_change']
                time_str = bos['time_str']
                
                # Sembolün genişliği için boşluk ekle (10 karakter)
                symbol_padded = f"{symbol:<10}"
                
                logger.info(f"🔴 {symbol_padded} | {tf:<3} | Fiyat: {price} ({pct:+.1f}%) | {time_str}")
        
        # BOS bulunamadıysa bildir
        if not bullish_bos and not bearish_bos:
            logger.info("-- BOS bulunamadı --")
        
        logger.info("===================================")
        
        return "BOS özet raporu oluşturuldu."


# Test için
if __name__ == "__main__":
    analyzer = BOSAnalyzer()
    # Test verisi oluştur
    test_swings = [
        {'type': 'HL', 'price': 100.0, 'timestamp': pd.Timestamp('2023-01-01'), 'index': 1},
        {'type': 'HH', 'price': 120.0, 'timestamp': pd.Timestamp('2023-01-02'), 'index': 2},
        {'type': 'LL', 'price': 90.0, 'timestamp': pd.Timestamp('2023-01-03'), 'index': 3},
    ]
    result = analyzer.detect_bos(test_swings)
    print(result) 