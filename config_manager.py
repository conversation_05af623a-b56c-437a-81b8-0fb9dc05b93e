import os
import json
from typing import Dict, Any, Optional
from loguru import logger

def load_config(config_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Konfigürasyon dosyasından veya çevre değişkenlerinden ayarları yükler
    
    Args:
        config_file: İsteğe bağlı konfigürasyon dosyası yolu
        
    Returns:
        Dict: Konfigürasyon ayarları
    """
    config = {
        "api_key": os.getenv("API_KEY", ""),
        "api_secret": os.getenv("API_SECRET", ""),
        "telegram_enabled": os.getenv("TELEGRAM_ENABLED", "false").lower() == "true",
        "telegram_bot_token": os.getenv("TELEGRAM_BOT_TOKEN", ""),
        "telegram_chat_id": os.getenv("TELEGRAM_CHAT_ID", ""),
        "log_level": os.getenv("LOG_LEVEL", "INFO"),
        "symbols": os.getenv("SYMBOLS", "BTCUSDT,ETHUSDT,SOLUSDT").split(","),
        "timeframes": os.getenv("TIMEFRAMES", "240").split(","),        "max_candles": int(os.getenv("MAX_CANDLES", "230")),
        "analysis_interval": int(os.getenv("ANALYSIS_INTERVAL", "480")),
        "npoc_sfp_update_interval": int(os.getenv("NPOC_SFP_UPDATE_INTERVAL", "3600")),
        "entry_timeout_hours": int(os.getenv("ENTRY_TIMEOUT_HOURS", "20")),  # Entry timeout süresi
        # Ek konfigürasyonlar buraya eklenebilir
    }
    
    # Dosyadan konfigürasyon yükle (varsa)
    if config_file:
        try:
            with open(config_file, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
            logger.info(f"Konfigürasyon dosyası ({config_file}) yüklendi.")
        except FileNotFoundError:
            logger.warning(f"Konfigürasyon dosyası bulunamadı: {config_file}")
        except Exception as e:
            logger.error(f"Konfigürasyon dosyası yüklenemedi: {str(e)}")
            
    # Ortam değişkenlerinin config dosyasını ezmesini sağla (isteğe bağlı)
    for key in config:
        env_val = os.getenv(key.upper())
        if env_val is not None:
            # Tip dönüşümü yap (örneğin bool, int)
            if isinstance(config[key], bool):
                config[key] = env_val.lower() == 'true'
            elif isinstance(config[key], int):
                try:
                    config[key] = int(env_val)
                except ValueError:
                     logger.warning(f"Ortam değişkeni {key.upper()} için geçersiz tamsayı değeri: {env_val}")
            elif isinstance(config[key], list) and isinstance(config[key][0], str):
                 config[key] = env_val.split(',')
            else:
                config[key] = env_val

    logger.info("Konfigürasyon yüklendi.")
    logger.debug(f"Yüklenen Konfigürasyon: {config}") # Hassas bilgileri loglamamaya dikkat edin
    return config
