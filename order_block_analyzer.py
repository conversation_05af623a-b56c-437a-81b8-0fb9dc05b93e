import pandas as pd
from loguru import logger
from typing import Dict, Optional, Tuple
from utils import format_price_standard

class OrderBlockAnalyzer:
    """
    Basitleştirilmiş Order Block (OB) tespiti yapar.
    - Bullish OB: G<PERSON><PERSON><PERSON><PERSON> bir yukarı hareketten önceki son düş<PERSON><PERSON> mumu.
    - Bearish OB: <PERSON><PERSON><PERSON><PERSON><PERSON> bir aşağı hareketten önceki son yükseliş mumu.
    """

    def __init__(self, lookback: int = 10, move_threshold_multiplier: float = 1.5):
        """
        OrderBlockAnalyzer'ı başlatır.

        Args:
            lookback (int): OB sonrası hareketin teyidi için bakılacak mum sayısı.
            move_threshold_multiplier (float): Bir hareketin 'güçlü' sayılması için
                                               OB mumunun aralığının kaç katı olması gerektiği.
        """
        self.lookback = lookback
        self.move_threshold_multiplier = move_threshold_multiplier
        logger.info(f"OrderBlockAnalyzer başlatıldı: lookback={lookback}, move_threshold={move_threshold_multiplier}")

    def _find_latest_bullish_ob(self, candles: pd.DataFrame) -> Optional[Dict]:
        """Verilen mumlarda en son Bullish OB'yi bulur."""
        latest_ob = None
        # Mumları sondan başa doğru tara
        for i in range(len(candles) - self.lookback - 1, 0, -1):
            current_candle = candles.iloc[i]
            prev_candle = candles.iloc[i-1] # OB adayı bu olabilir

            # 1. Aday Mum Düşüş Mumu mu? (Önceki mum)
            if prev_candle['close'] < prev_candle['open']:
                ob_candidate = prev_candle
                ob_range = ob_candidate['high'] - ob_candidate['low']
                if ob_range == 0: continue # Sıfır aralıklı mumları atla

                # 2. Sonraki Mumlar Güçlü Yükseliş Gösteriyor mu?
                #    OB'nin high seviyesini aşan ve OB aralığının X katı kadar hareket eden
                subsequent_candles = candles.iloc[i : i + self.lookback]
                max_high_after = subsequent_candles['high'].max()
                min_low_after = subsequent_candles['low'].min() # Düşüş kontrolü

                # OB'nin high'ı yukarı kırıldı mı ve anlamlı bir hareket oldu mu?
                if max_high_after > ob_candidate['high']:
                    move_up = max_high_after - ob_candidate['high']
                    # Hareket, OB mum aralığının belirli bir katından büyük mü?
                    # Ve bu süreçte OB'nin low'u aşağı kırılmadı mı?
                    if move_up > (ob_range * self.move_threshold_multiplier) and min_low_after >= ob_candidate['low']:
                        latest_ob = {
                            # OB'nin low fiyatını referans alalım
                            'price': ob_candidate['low'],
                            'high': ob_candidate['high'],
                            'low': ob_candidate['low'],
                            'timestamp': ob_candidate['timestamp']
                        }
                        # En sonuncuyu bulduğumuz için döngüden çık
                        logger.debug(f"Bullish OB bulundu: Time={latest_ob['timestamp']}, Low={format_price_standard(latest_ob['low'])}, High={format_price_standard(latest_ob['high'])}")
                        break
        return latest_ob

    def _find_latest_bearish_ob(self, candles: pd.DataFrame) -> Optional[Dict]:
        """Verilen mumlarda en son Bearish OB'yi bulur."""
        latest_ob = None
        for i in range(len(candles) - self.lookback - 1, 0, -1):
            current_candle = candles.iloc[i]
            prev_candle = candles.iloc[i-1] # OB adayı

            # 1. Aday Mum Yükseliş Mumu mu?
            if prev_candle['close'] > prev_candle['open']:
                ob_candidate = prev_candle
                ob_range = ob_candidate['high'] - ob_candidate['low']
                if ob_range == 0: continue

                # 2. Sonraki Mumlar Güçlü Düşüş Gösteriyor mu?
                subsequent_candles = candles.iloc[i : i + self.lookback]
                min_low_after = subsequent_candles['low'].min()
                max_high_after = subsequent_candles['high'].max() # Yükseliş kontrolü

                # OB'nin low'u aşağı kırıldı mı ve anlamlı bir hareket oldu mu?
                if min_low_after < ob_candidate['low']:
                    move_down = ob_candidate['low'] - min_low_after
                    # Hareket, OB mum aralığının belirli bir katından büyük mü?
                    # Ve bu süreçte OB'nin high'ı yukarı kırılmadı mı?
                    if move_down > (ob_range * self.move_threshold_multiplier) and max_high_after <= ob_candidate['high']:
                        latest_ob = {
                            # OB'nin high fiyatını referans alalım
                            'price': ob_candidate['high'],
                            'high': ob_candidate['high'],
                            'low': ob_candidate['low'],
                            'timestamp': ob_candidate['timestamp']
                        }
                        logger.debug(f"Bearish OB bulundu: Time={latest_ob['timestamp']}, Low={format_price_standard(latest_ob['low'])}, High={format_price_standard(latest_ob['high'])}")
                        break
        return latest_ob

    def find_latest_order_blocks(self, candles: pd.DataFrame) -> Dict[str, Optional[Dict]]:
        """
        Verilen mum verilerindeki en son Bullish ve Bearish Order Block'ları bulur.

        Args:
            candles (pd.DataFrame): OHLCV verilerini içeren DataFrame.
                                    'timestamp', 'open', 'high', 'low', 'close' sütunları olmalı.

        Returns:
            Dict[str, Optional[Dict]]: Bullish ve Bearish OB bilgilerini içeren sözlük.
                                       Örn: {'bullish': ob_data, 'bearish': ob_data}
                                       OB bulunamazsa ilgili anahtarın değeri None olur.
                                       ob_data formatı: {'price': float, 'high': float, 'low': float, 'timestamp': pd.Timestamp}
        """
        if candles is None or len(candles) < self.lookback + 2:
            logger.warning("Order Block analizi için yetersiz veri.")
            return {'bullish': None, 'bearish': None}

        # Önceki mumları da içerecek şekilde yeterli veri olduğundan emin ol
        candles_df = candles.copy()

        latest_bullish = self._find_latest_bullish_ob(candles_df)
        latest_bearish = self._find_latest_bearish_ob(candles_df)

        return {'bullish': latest_bullish, 'bearish': latest_bearish}

# Test için (opsiyonel)
if __name__ == '__main__':
    # Örnek bir DataFrame oluştur veya bir dosyadan oku
    # data = {...}
    # test_candles = pd.DataFrame(data)
    # test_candles['timestamp'] = pd.to_datetime(test_candles['timestamp'])
    # analyzer = OrderBlockAnalyzer()
    # obs = analyzer.find_latest_order_blocks(test_candles)
    # print(obs)
    pass 