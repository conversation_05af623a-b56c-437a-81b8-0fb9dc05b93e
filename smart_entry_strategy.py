from typing import Dict, Any, Optional, List
import pandas as pd
from loguru import logger

class SmartEntryStrategy:
    """
    Farklı piyasa rejimleri ve volatilite koşullarına göre akıllı giriş,
    stop-loss ve take-profit seviyeleri hesaplayan gelişmiş strateji sınıfı.
    YENİ VERSİYON: İdeal giriş uzaksa veya bulunamazsa piyasa fiyatından girme (fallback) özelliği eklendi.
    """

    def __init__(self):
        """
        SmartEntryStrategy sınıfını başlatır ve tüm strateji parametrelerini merkezileştirir.
        """
        logger.info("Akıllı Giriş Stratejisi modülü başlatıldı")

        # === Merkezileştirilmiş Eşik Değerleri ===
        self.max_entry_distance_pct = 0.01  # İdeal girişin fiyattan en fazla %1 uzakta olmasına izin ver.
        self.fvg_weight = 0.6  # FVG ve Fib birleştirme stratejisi kaldırıldı, ancak ileride kullanılabilir.
        self.fibonacci_weight = 0.4

        # === Stop-Loss ve Risk Yönetimi Parametreleri ===
        self.max_sl_pct = 0.04  # İzin verilen maksimum SL yüzdesi (%4).
        self.min_sl_pct = 0.008 # İzin verilen minimum SL yüzdesi (%0.8).
        self.default_sl_pct = 0.025  # Yedek olarak kullanılacak standart stop yüzdesi (%2.5).
        self.structure_sl_buffer_pct = 0.001  # Yapısal stop'a eklenen tampon payı (%0.1).

        # === Take Profit Sistem Konfigürasyonu ===
        self.tp1_rr_ratio = 1.0
        self.tp1_5_rr_ratio = 1.5
        self.tp2_rr_ratio = 2.0
        self.tp3_rr_ratio = 3.0

    def _get_float_levels(self, fibonacci_data: Optional[Dict[str, Any]]) -> Dict[float, Dict[str, Any]]:
        """
        Fibonacci verilerindeki 'levels' sözlüğünü güvenli bir şekilde {float: data} formatına çevirir.
        """
        float_levels = {}
        if not fibonacci_data or not isinstance(fibonacci_data.get('levels'), dict):
            return float_levels

        for key, value in fibonacci_data['levels'].items():
            try:
                float_key = float(key)
                float_levels[float_key] = value
            except (ValueError, TypeError):
                continue
        return float_levels

    def calculate_entry_levels(self, symbol: str, stats: Dict[str, Any],
                               trade_direction: str, fibonacci_data: Optional[Dict[str, Any]],
                               order_blocks: Optional[Dict[str, Any]] = None,
                               swing_points: Optional[List[Dict[str, Any]]] = None,
                               pattern_name: Optional[str] = None,
                               candles: Optional[pd.DataFrame] = None,
                               fvg_data: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Tüm analizleri birleştiren ana orkestratör fonksiyonu. İdeal giriş bulamazsa
        veya giriş çok uzaksa, anlık fiyattan girer.
        """
        entry_levels = {"primary_entry": None, "strategy_used": "none", "pattern_name": pattern_name}
        last_price = stats.get("last_price")
        if not last_price:
            return entry_levels

        # Öncelikli pattern kontrolü (TRIT/TRIB)
        if pattern_name and ("TRIT" in pattern_name.upper() or "TRIB" in pattern_name.upper()):
            logger.info(f"[{symbol}] 🚨 ÖZEL STRATEJİ: '{pattern_name}' için anında giriş.")
            entry_levels["primary_entry"] = last_price
            entry_levels["strategy_used"] = f"{pattern_name}_direct"
        else:
            # Diğer durumlar için ideal girişi belirle
            ideal_entry_result = self._determine_ideal_entry(symbol, stats, trade_direction, fibonacci_data, order_blocks)

            if ideal_entry_result and ideal_entry_result.get("primary_entry"):
                ideal_price = ideal_entry_result["primary_entry"]
                distance_pct = abs(ideal_price - last_price) / last_price

                # Eğer ideal giriş çok uzaksa, anlık fiyattan gir
                if distance_pct > self.max_entry_distance_pct:
                    logger.warning(f"[{symbol}] İdeal giriş ({ideal_price:.4f}) mevcut fiyattan "
                                   f"çok uzak (%{distance_pct*100:.2f}). Anlık fiyattan giriş yapılıyor.")
                    entry_levels["primary_entry"] = last_price
                    entry_levels["strategy_used"] = "market_entry_fallback"
                else:
                    # İdeal giriş makul mesafede, onu kullan
                    entry_levels.update(ideal_entry_result)
            else:
                # Hiç ideal giriş bulunamadıysa, anlık fiyattan gir
                logger.warning(f"[{symbol}] Hiçbir ideal giriş stratejisi sonuç vermedi. Anlık fiyattan giriş yapılıyor.")
                entry_levels["primary_entry"] = last_price
                entry_levels["strategy_used"] = "market_entry_fallback"

        # Nihai olarak belirlenen giriş fiyatı üzerinden SL/TP hesapla
        if entry_levels.get("primary_entry"):
            sl_tp = self._calculate_sl_tp(
                symbol,
                entry_levels["primary_entry"],
                trade_direction,
                swing_points,
                pattern_name,
                fibonacci_data
            )
            entry_levels.update(sl_tp)

        return entry_levels

    def _determine_ideal_entry(self, symbol: str, stats: Dict[str, Any], trade_direction: str,
                               fibonacci_data: Optional[Dict[str, Any]],
                               order_blocks: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Farklı stratejileri deneyerek en iyi giriş noktasını bulur (basitleştirilmiş)."""
        last_price = stats.get("last_price")

        # Öncelik 1: Fibonacci Girişi
        if fibonacci_data:
            fib_entry = self._calculate_fibonacci_entry(symbol, fibonacci_data, last_price, trade_direction)
            if fib_entry:
                logger.info(f"[{symbol}] ✅ Strateji Bulundu: Fibonacci")
                return fib_entry

        # Öncelik 2: Order Block Girişi
        if order_blocks:
            ob_entry = self._calculate_order_block_entry(symbol, order_blocks, last_price, trade_direction, fibonacci_data)
            if ob_entry:
                logger.info(f"[{symbol}] ✅ Strateji Bulundu: Order Block")
                return ob_entry

        return None

    def _calculate_fibonacci_entry(self, symbol: str, fibonacci_data: Dict[str, Any], last_price: float, trade_direction: str) -> Optional[Dict[str, Any]]:
        """En yakın ve mantıklı Fibonacci seviyesini giriş olarak belirler."""
        float_levels = self._get_float_levels(fibonacci_data)
        if not float_levels:
            return None

        potential_entries = []
        for level, data in float_levels.items():
            price = data.get('price')
            if not price: continue

            # Yöne uygun seviyeleri filtrele (long için fiyatın altında, short için üstünde olmalı)
            if (trade_direction == "BULL" and price < last_price) or \
               (trade_direction == "BEAR" and price > last_price):
                distance = abs(last_price - price)
                potential_entries.append({'price': price, 'distance': distance, 'level_name': str(level)})

        if not potential_entries:
            return None

        # Fiyata en yakın olanı seç
        best_entry = min(potential_entries, key=lambda x: x['distance'])
        logger.debug(f"[{symbol}] En yakın Fib seviyesi ({best_entry['level_name']}) {best_entry['price']:.4f} olarak seçildi.")
        return {"primary_entry": best_entry['price'], "strategy_used": "fibonacci_nearest", "fib_level": best_entry['level_name']}

    def _calculate_order_block_entry(self, symbol: str, order_blocks: List[Dict[str, Any]], last_price: float, trade_direction: str, fibonacci_data: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Order block'ları ve potansiyel Fibonacci birleşimlerini kullanarak giriş hesaplar."""
        ob_to_use = None
        if trade_direction == "BULL":
            bullish_obs = [ob for ob in order_blocks if ob.get('type') == 'bullish' and not ob.get('mitigated') and ob.get('top') < last_price]
            if bullish_obs:
                ob_to_use = min(bullish_obs, key=lambda ob: last_price - ob.get('top'))
                entry_price = ob_to_use.get('top')
                strategy = "order_block_bullish"
        elif trade_direction == "BEAR":
            bearish_obs = [ob for ob in order_blocks if ob.get('type') == 'bearish' and not ob.get('mitigated') and ob.get('bottom') > last_price]
            if bearish_obs:
                ob_to_use = min(bearish_obs, key=lambda ob: ob.get('bottom') - last_price)
                entry_price = ob_to_use.get('bottom')
                strategy = "order_block_bearish"

        if not ob_to_use:
            return None

        # Fibonacci ile kesişim kontrolü
        fib_confluence_level = None
        float_levels = self._get_float_levels(fibonacci_data)
        if float_levels:
            for level, data in float_levels.items():
                fib_price = data['price']
                if ob_to_use.get('bottom') <= fib_price <= ob_to_use.get('top'):
                    entry_price = (entry_price + fib_price) / 2  # Girişi optimize et
                    fib_confluence_level = str(level)
                    strategy += "_fib_confluence"
                    logger.info(f"[{symbol}] OB/Fib kesişimi bulundu! Seviye: {fib_confluence_level}, Yeni Giriş: {entry_price:.4f}")
                    break

        return {"primary_entry": entry_price, "strategy_used": strategy, "fib_level": fib_confluence_level}

    def _calculate_sl_tp(self, symbol: str, entry_price: float, trade_direction: str,
                         swing_points: Optional[List[Dict[str, Any]]],
                         pattern_name: Optional[str],
                         fibonacci_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Stop-Loss ve Take-Profit seviyelerini, yapısal ve yüzdesel mantıkla hesaplar.
        TRBUSDT döngü sorununu önlemek için `sl_type` bilgisini döndürür.
        """
        sl_tp = {}
        if not entry_price:
            return sl_tp

        final_sl = None

        # 1. Yapısal Stop Belirlemeyi Dene
        if swing_points:
            structural_pivot = None
            if trade_direction == "BULL":
                protective_lows = [s for s in swing_points if s.get('type') in ['LL', 'HL'] and s['price'] < entry_price]
                if protective_lows:
                    structural_pivot = min(protective_lows, key=lambda x: x['price'])
            elif trade_direction == "BEAR":
                protective_highs = [s for s in swing_points if s.get('type') in ['HH', 'LH'] and s['price'] > entry_price]
                if protective_highs:
                    structural_pivot = max(protective_highs, key=lambda x: x['price'])

            if structural_pivot:
                buffer = structural_pivot['price'] * self.structure_sl_buffer_pct
                structural_sl_price = structural_pivot['price'] - buffer if trade_direction == "BULL" else structural_pivot['price'] + buffer

                risk_pct = abs(entry_price - structural_sl_price) / entry_price

                # Stop-hunt önleme kuralı: Risk makul aralıkta ise yapısal stop kullan
                if self.min_sl_pct <= risk_pct <= self.max_sl_pct:
                    final_sl = structural_sl_price
                    sl_tp["sl_type"] = 'structural_safe'
                    logger.info(f"[{symbol}] ✅ Güvenli Yapısal Stop bulundu ve kabul edildi: {final_sl:.4f} (Risk: %{risk_pct*100:.2f})")
                else:
                    logger.warning(f"[{symbol}] Yapısal stop ({structural_sl_price:.4f}) çok yakın/uzak (Risk: %{risk_pct*100:.2f}). Standart % stop kullanılacak.")

        # 2. Varsayılan Stop (Yedek Mekanizma)
        if final_sl is None:
            if trade_direction == "BULL":
                final_sl = entry_price * (1 - self.default_sl_pct)
            else: # BEAR
                final_sl = entry_price * (1 + self.default_sl_pct)
            sl_tp["sl_type"] = 'default_percentage'
            logger.info(f"[{symbol}] ⚠️ Standart %{(self.default_sl_pct * 100):.1f} Stop kullanılıyor: {final_sl:.4f}")

        # 3. Nihai Seviyeleri Hesapla
        sl_tp["stop_loss"] = final_sl
        risk_amount = abs(entry_price - final_sl)

        # TP'ler R:R'a göre hesaplanır
        if trade_direction == "BULL":
            sl_tp["tp1"] = entry_price + (risk_amount * self.tp1_rr_ratio)
            sl_tp["tp1_5"] = entry_price + (risk_amount * self.tp1_5_rr_ratio)
            sl_tp["tp2"] = entry_price + (risk_amount * self.tp2_rr_ratio)
            sl_tp["tp3"] = entry_price + (risk_amount * self.tp3_rr_ratio)
        elif trade_direction == "BEAR":
            sl_tp["tp1"] = entry_price - (risk_amount * self.tp1_rr_ratio)
            sl_tp["tp1_5"] = entry_price - (risk_amount * self.tp1_5_rr_ratio)
            sl_tp["tp2"] = entry_price - (risk_amount * self.tp2_rr_ratio)
            sl_tp["tp3"] = entry_price - (risk_amount * self.tp3_rr_ratio)

        sl_tp["sl_percentage"] = (risk_amount / entry_price) * 100
        if sl_tp.get("tp1"):
            tp1_profit_dollars = abs(sl_tp["tp1"] - entry_price)
            sl_tp["tp_percentage"] = (tp1_profit_dollars / entry_price) * 100

        return sl_tp