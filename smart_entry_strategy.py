from typing import Dict, Any, Optional, List
import pandas as pd
from loguru import logger

class SmartEntryStrategy:
    """
    Farklı piyasa rejimleri ve volatilite koşullarına göre akıllı giriş,
    stop-loss ve take-profit seviyeleri hesaplayan gelişmiş strateji sınıfı.
    """

    def __init__(self):
        """
        SmartEntryStrategy sınıfını başlatır ve tüm strateji parametrelerini merkezileştirir.
        """
        logger.info("Akıllı Giriş Stratejisi modülü başlatıldı")

        # === Merkezileştirilmiş Eşik Değerleri ===
        self.max_fvg_distance_pct = 0.02
        self.fvg_weight = 0.6
        self.fibonacci_weight = 0.4

        # === Stop-Loss ve Risk Yönetimi Parametreleri (GÜNCELLENDİ) ===
        self.structural_stop_min_risk_pct = 2.0  # YENİ: Yapısal stop'un kabul edilmesi için gereken minimum risk yüzdesi.
        self.default_sl_pct = 0.025              # Yedek olarak kullanılacak standart stop yüzdesi (%2.5).
        self.structure_sl_buffer_pct = 0.001     # Yapısal stop'a eklenen tampon payı (%0.1).

        # ESKİ parametreleri silebilir veya yoruma alabilirsiniz.
        # self.max_sl_pct = 0.04
        # self.min_sl_pct = 0.008
        
        # === Take Profit Sistem Konfigürasyonu ===
        # TP stratejisi: "classic", "fibonacci", "hybrid"
        self.tp_strategy = "hybrid"
        
        # Klasik R:R Oranları
        self.tp1_rr_ratio = 1.0
        self.tp1_5_rr_ratio = 1.5  # Yeni TP1.5 seviyesi
        self.tp2_rr_ratio = 2.0
        self.tp3_rr_ratio = 3.0
        
        # Hibrit sistem için eşik değerleri
        self.fib_proximity_threshold = 0.002  # %0.2 yakınlık eşiği
        self.max_fib_adjustment = 0.001  # Maksimum %0.1 ayarlama

    def _get_float_levels(self, fibonacci_data: Optional[Dict[str, Any]]) -> Dict[float, Dict[str, Any]]:
        """
        Fibonacci verilerindeki 'levels' sözlüğünü güvenli bir şekilde
        {float: data} formatına çevirir. Hem string hem de float anahtarları işler.
        """
        float_levels = {}
        if not fibonacci_data or not isinstance(fibonacci_data.get('levels'), dict):
            return float_levels

        for key, value in fibonacci_data['levels'].items():
            try:
                float_key = float(key)
                float_levels[float_key] = value
            except (ValueError, TypeError):
                continue
        return float_levels

    def calculate_entry_levels(self, symbol: str, stats: Dict[str, Any], 
                               trade_direction: str, fibonacci_data: Optional[Dict[str, Any]],
                               order_blocks: Optional[Dict[str, Any]] = None,
                               swing_points: Optional[List[Dict[str, Any]]] = None,
                               pattern_name: Optional[str] = None,
                               candles: Optional[pd.DataFrame] = None,
                               fvg_data: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Belirli bir sembol ve işlem yönü için ideal giriş seviyelerini hesaplar.
        """
        entry_levels = {"primary_entry": None, "strategy_used": "none", "pattern_name": pattern_name}
        last_price = stats.get("last_price")
        if not last_price:
            return entry_levels

        if pattern_name and ("TRIT" in pattern_name.upper() or "TRIB" in pattern_name.upper()):
            logger.info(f"[{symbol}] 🚨 ÖZEL STRATEJİ: '{pattern_name}' için anında giriş.")
            entry_levels["primary_entry"] = last_price
            entry_levels["strategy_used"] = f"{pattern_name} Pattern Direct"
            if sl_tp := self._calculate_sl_tp(last_price, trade_direction, swing_points, pattern_name, fibonacci_data):
                entry_levels.update(sl_tp)
            return entry_levels

        # === YENİ STRATEJİ SIRASI: FİBONACCI ÖNCELİKLİ ===
        
        # ÖNCELİK 1: Sadece Fibonacci
        fib_entry_found = False
        if fibonacci_data:
            fib_entry = self._calculate_fibonacci_entry(symbol, fibonacci_data, last_price, trade_direction)
            if fib_entry:
                entry_levels.update(fib_entry)
                fib_entry_found = True
                logger.info(f"[{symbol}] ✅ Strateji Seçildi: Fibonacci (Öncelik 1)")

        # ÖNCELİK 2: FVG + Fibonacci Birleşimi (sadece Fibonacci bulunamazsa)
        fvg_entry_found = False
        if not fib_entry_found and fvg_data and fibonacci_data:
            fvg_entry = self._calculate_fvg_fibonacci_entry(symbol, fvg_data, fibonacci_data, last_price, trade_direction)
            if fvg_entry:
                distance_to_entry = abs(last_price - fvg_entry["primary_entry"]) / last_price
                if distance_to_entry <= self.max_fvg_distance_pct:
                    entry_levels.update(fvg_entry)
                    fvg_entry_found = True
                    logger.info(f"[{symbol}] ✅ Strateji Seçildi: FVG+Fibonacci (Öncelik 2)")
                else:
                    logger.warning(f"[{symbol}] FVG+Fib girişi çok uzak (%{distance_to_entry*100:.2f}), Order Block denenecek.")

        # ÖNCELİK 3: Order Block + Fibonacci Birleşimi (diğerleri bulunamazsa)
        ob_entry_found = False
        if not fib_entry_found and not fvg_entry_found and order_blocks:
            ob_entry = self._calculate_order_block_entry(symbol, order_blocks, last_price, trade_direction, fibonacci_data)
            if ob_entry:
                distance_to_entry = abs(last_price - ob_entry["primary_entry"]) / last_price
                if distance_to_entry <= self.max_fvg_distance_pct:
                    entry_levels.update(ob_entry)
                    ob_entry_found = True
                    logger.info(f"[{symbol}] ✅ Strateji Seçildi: Order Block (+Fib Confluence) (Öncelik 3)")
                else:
                    logger.warning(f"[{symbol}] Order Block girişi çok uzak (%{distance_to_entry*100:.2f})")

        # Eğer hiçbir strateji başarılı olmadıysa
        if not (fib_entry_found or fvg_entry_found or ob_entry_found):
            logger.warning(f"[{symbol}] ❌ Hiçbir giriş stratejisi başarılı olmadı.")
        if entry_levels.get("primary_entry"):
            if sl_tp := self._calculate_sl_tp(entry_levels["primary_entry"], trade_direction, swing_points, pattern_name, fibonacci_data):
                entry_levels.update(sl_tp)
        else:
            logger.warning(f"[{symbol}] ❌ Hiçbir giriş stratejisi başarılı olmadı.")
        
        # Basit volatilite seviyesi (ATR olmadan)
        entry_levels.update({"volatility_level": "unknown", "market_regime": "simplified"})
        return entry_levels

    def _calculate_fvg_fibonacci_entry(self, symbol: str, fvg_data: List[Dict[str, Any]], fibonacci_data: Dict[str, Any], last_price: float, trade_direction: str) -> Optional[Dict[str, Any]]:
        """FVG bölgeleri ile Fibonacci seviyelerini kombine ederek giriş hesaplar."""
        float_levels = self._get_float_levels(fibonacci_data)
        if not fvg_data or not float_levels:
            return None

        suitable_fvgs = [
            {'fvg': fvg, 'eq': fvg['eq'], 'distance': abs(last_price - fvg['eq']) / last_price}
            for fvg in fvg_data if not fvg.get('filled') and fvg.get('type', '').startswith(trade_direction) and fvg.get('eq')
        ]
        if not suitable_fvgs:
            return None
        closest_fvg = min(suitable_fvgs, key=lambda x: x['distance'])
        fvg_eq = closest_fvg['fvg']['eq']

        fib_targets = [{'level': lvl, 'price': data['price'], 'distance': abs(fvg_eq - data['price'])} for lvl, data in float_levels.items()]
        if not fib_targets:
            return None
        closest_fib = min(fib_targets, key=lambda x: x['distance'])

        final_entry = (fvg_eq * self.fvg_weight) + (closest_fib['price'] * self.fibonacci_weight)

        if (trade_direction == "bull" and final_entry > last_price) or (trade_direction == "bear" and final_entry < last_price):
            return None

        return {"primary_entry": final_entry, "strategy_used": "fvg_fibonacci", "fib_level": str(closest_fib['level']), "fvg_eq": fvg_eq}

    def _calculate_fibonacci_entry(self, symbol: str, fibonacci_data: Dict[str, Any], last_price: float, trade_direction: str) -> Optional[Dict[str, Any]]:
        """Basitleştirilmiş Fibonacci giriş seviyesi hesaplar - en yakın seviyeyi kullanır."""
        float_levels = self._get_float_levels(fibonacci_data)
        if not float_levels:
            return None

        # Yöne göre geçerli olan tüm Fibonacci hedeflerini bul ve fiyata olan uzaklıklarına göre sırala
        fibonacci_targets = [
            {'name': str(level), 'price': data['price'], 'distance': abs(last_price - data['price'])}
            for level, data in float_levels.items()
            if ((trade_direction == "bull" and data['price'] < last_price) or (trade_direction == "bear" and data['price'] > last_price))
        ]

        if not fibonacci_targets:
            logger.warning(f"[{symbol}] Sinyal var ancak fiyata göre mantıklı bir Fib seviyesi bulunamadı.")
            return None
        
        # Fiyata en yakın seviyeyi seç
        fibonacci_targets.sort(key=lambda x: x["distance"])
        selected_target = fibonacci_targets[0]

        logger.info(f"[{symbol}] ✅ En yakın Fibonacci seviyesi ({selected_target['name']}) hedeflendi.")
        
        return {
            "primary_entry": selected_target['price'], 
            "strategy_used": "fibonacci_simple", 
            "fib_level": selected_target['name']
        }

    def _calculate_order_block_entry(self, symbol: str, order_blocks: List[Dict[str, Any]], last_price: float, trade_direction: str, fibonacci_data: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Order block'ları ve potansiyel Fibonacci birleşimlerini kullanarak giriş hesaplar."""
        try:
            if not order_blocks:
                return None

            float_levels = self._get_float_levels(fibonacci_data)

            if trade_direction == "bull":
                candidate_obs = [ob for ob in order_blocks if ob.get('type') == 'bullish' and not ob.get('mitigated') and ob.get('top') < last_price]
                if not candidate_obs:
                    return None
                closest_ob = min(candidate_obs, key=lambda ob: last_price - ob.get('top'))
                entry_price = closest_ob.get('top')
                strategy = "order_block_bullish"
            elif trade_direction == "bear":
                candidate_obs = [ob for ob in order_blocks if ob.get('type') == 'bearish' and not ob.get('mitigated') and ob.get('bottom') > last_price]
                if not candidate_obs:
                    return None
                closest_ob = min(candidate_obs, key=lambda ob: ob.get('bottom') - last_price)
                entry_price = closest_ob.get('bottom')
                strategy = "order_block_bearish"
            else:
                return None

            # Confluence Check
            fib_confluence_level = None
            if float_levels:
                for level, data in float_levels.items():
                    fib_price = data['price']
                    # Check if fib_price is inside the OB body
                    if closest_ob.get('bottom') <= fib_price <= closest_ob.get('top'):
                        # We have a confluence! Refine entry price
                        entry_price = (entry_price + fib_price) / 2
                        fib_confluence_level = str(level)
                        strategy += "_fib_confluence"
                        logger.info(f"[{symbol}] OB/Fib confluence bulundu! Seviye: {fib_confluence_level}, Yeni Giriş: {entry_price:.4f}")
                        break  # Use the first confluence found

            return {"primary_entry": entry_price, "strategy_used": strategy, "ob_id": closest_ob.get('id', None), "fib_level": fib_confluence_level}
        except Exception as e:
            logger.error(f"Order block giriş hesaplama hatası: {e}")
            return None

    def _calculate_sl_tp(self, entry_price: float, trade_direction: str, 
                         swing_points: Optional[List[Dict[str, Any]]], 
                         pattern_name: Optional[str], 
                         fibonacci_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Stop-Loss ve Take-Profit seviyelerini, yüzdesel risk mantığı ile hesaplar.
        (ATR'siz, stabil versiyon)
        """
        sl_tp = {}
        try:
            if not entry_price:
                return {}

            final_sl = None
            
            # 1. Yapısal Stop Belirlemeyi Dene
            if swing_points:
                structural_pivot = None
                if trade_direction == "bull":
                    protective_lows = [s for s in swing_points if s.get('type') in ['LL', 'HL'] and s['price'] < entry_price]
                    if protective_lows:
                        structural_pivot = min(protective_lows, key=lambda x: x['price'])
                else: # bear
                    protective_highs = [s for s in swing_points if s.get('type') in ['HH', 'LH'] and s['price'] > entry_price]
                    if protective_highs:
                        structural_pivot = max(protective_highs, key=lambda x: x['price'])
                
                if structural_pivot:
                    buffer = structural_pivot['price'] * self.structure_sl_buffer_pct
                    structural_sl_price = structural_pivot['price'] - buffer if trade_direction == "bull" else structural_pivot['price'] + buffer
                    
                    risk_pct = (abs(entry_price - structural_sl_price) / entry_price) * 100
                    
                    # Stop-hunt önleme kuralımız: Risk %2.0'dan büyükse yapısal stop'u kullan
                    if risk_pct > self.structural_stop_min_risk_pct:
                        final_sl = structural_sl_price
                        sl_tp["sl_type"] = 'structural_safe'
                        logger.info(f"✅ Güvenli Yapısal Stop bulundu ve kabul edildi: {final_sl:.4f} (Risk: %{risk_pct:.2f})")
                    else:
                        logger.warning(f"Yapısal stop ({structural_sl_price:.4f}) çok yakın (Risk: %{risk_pct:.2f}). Standart % stop kullanılacak.")

            # 2. Varsayılan Stop (Yedek Mekanizma)
            if final_sl is None:
                if trade_direction == "bull":
                    final_sl = entry_price * (1 - self.default_sl_pct)
                else: # bear
                    final_sl = entry_price * (1 + self.default_sl_pct)
                sl_tp["sl_type"] = 'default_percentage'
                logger.info(f"⚠️ Standart %{(self.default_sl_pct * 100):.1f} Stop kullanılıyor: {final_sl:.4f}")
            
            # 3. Nihai Seviyeleri Hesapla
            sl_tp["stop_loss"] = final_sl
            risk_amount = abs(entry_price - final_sl)
            
            # TP'ler R:R'a göre hesaplanır
            tp_results = self._calculate_take_profit_levels(entry_price, risk_amount, trade_direction, fibonacci_data)
            sl_tp.update(tp_results)
            
            sl_tp["sl_percentage"] = (risk_amount / entry_price) * 100
            if tp_results.get("tp1"):
                tp1_profit_dollars = abs(tp_results["tp1"] - entry_price)
                sl_tp["tp_percentage"] = (tp1_profit_dollars / entry_price) * 100

            return sl_tp

        except Exception as e:
            logger.error(f"SL/TP hesaplama hatası: {e}", exc_info=True)
            return {}

    def _calculate_take_profit_levels(self, entry_price: float, risk_amount: float, trade_direction: str, fibonacci_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Hibrit Take Profit sistemi:
        - classic: Sadece R:R oranları kullanır
        - fibonacci: Sadece Fibonacci seviyeleri kullanır  
        - hybrid: R:R temel alır, yakın Fibonacci seviyeleri ile optimize eder
        """
        tp_levels = {"tp1": None, "tp1_5": None, "tp2": None, "tp3": None}
        
        try:
            if self.tp_strategy == "classic":
                return self._calculate_classic_tp(entry_price, risk_amount, trade_direction)
            elif self.tp_strategy == "fibonacci":
                return self._calculate_fibonacci_tp(entry_price, trade_direction, fibonacci_data)
            else:  # hybrid
                return self._calculate_hybrid_tp(entry_price, risk_amount, trade_direction, fibonacci_data)
                
        except Exception as e:
            logger.error(f"TP hesaplama hatası ({self.tp_strategy}): {e}")
            # Hata durumunda klasik sisteme geri dön
            return self._calculate_classic_tp(entry_price, risk_amount, trade_direction)

    def _calculate_classic_tp(self, entry_price: float, risk_amount: float, trade_direction: str) -> Dict[str, Any]:
        """Klasik R:R oranları ile TP hesaplama"""
        tp_levels = {}
        
        rr_ratios = {
            "tp1": self.tp1_rr_ratio,
            "tp1_5": self.tp1_5_rr_ratio,
            "tp2": self.tp2_rr_ratio,
            "tp3": self.tp3_rr_ratio
        }
        
        for tp_key, rr_ratio in rr_ratios.items():
            if trade_direction == "bull":
                tp_levels[tp_key] = entry_price + (risk_amount * rr_ratio)
            else:
                tp_levels[tp_key] = entry_price - (risk_amount * rr_ratio)
            
            logger.info(f"[TP-Klasik] {tp_key.upper()}: {tp_levels[tp_key]:.4f} (R:R {rr_ratio}:1)")
        
        return tp_levels

    def _calculate_fibonacci_tp(self, entry_price: float, trade_direction: str, fibonacci_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Sadece Fibonacci seviyeleri ile TP hesaplama (eski sistem)"""
        tp_levels = {"tp1": None, "tp1_5": None, "tp2": None, "tp3": None}
        float_levels = self._get_float_levels(fibonacci_data)
        
        if not float_levels:
            logger.warning("[TP-Fibonacci] Fibonacci verisi bulunamadı, klasik sisteme geçiliyor")
            return {"tp1": None, "tp1_5": None, "tp2": None, "tp3": None}
        
        all_fib_prices = {key: value['price'] for key, value in float_levels.items() if 'price' in value}
        potential_tps = [p for p in all_fib_prices.values() if 
                        (trade_direction == "bull" and p > entry_price) or 
                        (trade_direction == "bear" and p < entry_price)]
        
        potential_tps.sort(reverse=(trade_direction == "bear"))
        
        if potential_tps:
            logger.info(f"[TP-Fibonacci] {len(potential_tps)} adet Fibonacci hedefi bulundu")
            tp_keys = ["tp1", "tp1_5", "tp2", "tp3"]
            
            for i, tp_key in enumerate(tp_keys):
                if i < len(potential_tps):
                    tp_levels[tp_key] = potential_tps[i]
                    logger.info(f"[TP-Fibonacci] {tp_key.upper()}: {tp_levels[tp_key]:.4f}")
        else:
            logger.warning("[TP-Fibonacci] Uygun Fibonacci hedefi bulunamadı")
        
        return tp_levels

    def _calculate_hybrid_tp(self, entry_price: float, risk_amount: float, trade_direction: str, fibonacci_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Hibrit TP sistemi: R:R temel alır, yakın Fibonacci seviyeleri ile optimize eder
        """
        # Önce klasik R:R seviyelerini hesapla
        classic_tp = self._calculate_classic_tp(entry_price, risk_amount, trade_direction)
        
        # Fibonacci verileri varsa optimizasyon yap
        float_levels = self._get_float_levels(fibonacci_data)
        if not float_levels:
            logger.info("[TP-Hibrit] Fibonacci verisi yok, klasik R:R kullanılıyor")
            return classic_tp
        
        all_fib_prices = [data['price'] for data in float_levels.values() if 'price' in data]
        
        # Her R:R seviyesi için yakın Fibonacci seviyesi ara
        optimized_tp = {}
        
        for tp_key, classic_price in classic_tp.items():
            if classic_price is None:
                continue
                
            # Bu R:R seviyesine en yakın Fibonacci seviyesini bul
            closest_fib = self._find_closest_fibonacci(classic_price, all_fib_prices, trade_direction, entry_price)
            
            if closest_fib:
                distance_pct = abs(classic_price - closest_fib) / classic_price
                
                if distance_pct <= self.fib_proximity_threshold:
                    # Fibonacci seviyesi yeterince yakınsa, ağırlıklı ortalama al
                    weight_classic = 0.7
                    weight_fib = 0.3
                    optimized_price = (classic_price * weight_classic) + (closest_fib * weight_fib)
                    
                    # Maksimum ayarlama sınırını kontrol et
                    max_adjustment = classic_price * self.max_fib_adjustment
                    if abs(optimized_price - classic_price) <= max_adjustment:
                        optimized_tp[tp_key] = optimized_price
                        logger.info(f"[TP-Hibrit] {tp_key.upper()}: {classic_price:.4f} → {optimized_price:.4f} (Fib: {closest_fib:.4f})")
                    else:
                        optimized_tp[tp_key] = classic_price
                        logger.info(f"[TP-Hibrit] {tp_key.upper()}: {classic_price:.4f} (Fib çok uzak, ayarlama yapılmadı)")
                else:
                    optimized_tp[tp_key] = classic_price
                    logger.info(f"[TP-Hibrit] {tp_key.upper()}: {classic_price:.4f} (Yakın Fib bulunamadı)")
            else:
                optimized_tp[tp_key] = classic_price
                logger.info(f"[TP-Hibrit] {tp_key.upper()}: {classic_price:.4f} (R:R temel)")
        
        return optimized_tp

    def _find_closest_fibonacci(self, target_price: float, fib_prices: List[float], trade_direction: str, entry_price: float) -> Optional[float]:
        """Hedef fiyata en yakın, geçerli Fibonacci seviyesini bulur"""
        valid_fibs = [
            fib for fib in fib_prices 
            if ((trade_direction == "bull" and fib > entry_price) or 
                (trade_direction == "bear" and fib < entry_price))
        ]
        
        if not valid_fibs:
            return None
        
        # En yakın olanı bul
        closest = min(valid_fibs, key=lambda fib: abs(fib - target_price))
        return closest

    def set_tp_strategy(self, strategy: str) -> bool:
        """
        TP stratejisini değiştirir.
        
        Args:
            strategy: "classic", "fibonacci", "hybrid" değerlerinden biri
            
        Returns:
            bool: Başarılı ise True
        """
        valid_strategies = ["classic", "fibonacci", "hybrid"]
        if strategy.lower() not in valid_strategies:
            logger.error(f"Geçersiz TP stratejisi: {strategy}. Geçerli değerler: {valid_strategies}")
            return False
        
        old_strategy = self.tp_strategy
        self.tp_strategy = strategy.lower()
        logger.info(f"TP stratejisi değiştirildi: {old_strategy} → {self.tp_strategy}")
        return True

    def get_tp_strategy_info(self) -> Dict[str, Any]:
        """Mevcut TP stratejisi ve parametreleri hakkında bilgi döner"""
        return {
            "current_strategy": self.tp_strategy,
            "classic_rr_ratios": {
                "tp1": self.tp1_rr_ratio,
                "tp1_5": self.tp1_5_rr_ratio,
                "tp2": self.tp2_rr_ratio,
                "tp3": self.tp3_rr_ratio
            },
            "hybrid_settings": {
                "fib_proximity_threshold": self.fib_proximity_threshold,
                "max_fib_adjustment": self.max_fib_adjustment
            },
            "available_strategies": ["classic", "fibonacci", "hybrid"]
        }

    def update_rr_ratios(self, tp1: float = None, tp1_5: float = None, tp2: float = None, tp3: float = None) -> None:
        """R:R oranlarını günceller"""
        if tp1 is not None:
            self.tp1_rr_ratio = tp1
            logger.info(f"TP1 R:R oranı güncellendi: {tp1}")
        if tp1_5 is not None:
            self.tp1_5_rr_ratio = tp1_5
            logger.info(f"TP1.5 R:R oranı güncellendi: {tp1_5}")
        if tp2 is not None:
            self.tp2_rr_ratio = tp2
            logger.info(f"TP2 R:R oranı güncellendi: {tp2}")
        if tp3 is not None:
            self.tp3_rr_ratio = tp3
            logger.info(f"TP3 R:R oranı güncellendi: {tp3}")