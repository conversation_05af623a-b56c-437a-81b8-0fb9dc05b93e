import pandas as pd
import numpy as np
from utils import format_price_standard

class SuperTrend:
    def __init__(self, period=10, multiplier=3.0, atr_method="rma"):
        """
        SuperTrend indikatörü
        
        Parametreler:
        period (int): ATR hesaplama periyodu
        multiplier (float): ATR çarpanı
        atr_method (str): ATR hesaplama yöntemi ('rma' veya 'sma')
        """
        self.period = period
        self.multiplier = multiplier
        self.atr_method = atr_method
        
    def calculate(self, df):
        """
        SuperTrend değerlerini hesaplar
        
        Parametreler:
        df (pandas.DataFrame): En az high, low, close sütunları olan DataFrame
        
        Dönüş:
        pandas.DataFrame: SuperTrend değerleri eklenmiş DataFrame
        """
        # Temel kontroller
        required_columns = ['high', 'low', 'close']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"DataFrame'de {col} sütunu bulunamadı")
        
        # Veri kopyası oluştur
        result_df = df.copy()
        
        # Kaynak hesapla (hl2 - high low ortalaması)
        result_df['source'] = (result_df['high'] + result_df['low']) / 2
        
        # True Range hesapla
        result_df['tr'] = self._calculate_tr(result_df)
        
        # ATR hesapla
        if self.atr_method == 'rma':
            result_df['atr'] = self._calculate_rma(result_df['tr'], self.period)
        else:
            result_df['atr'] = result_df['tr'].rolling(window=self.period).mean()
        
        # SuperTrend hesapla
        result_df = self._calculate_supertrend(result_df)
        
        # Trend yönü ve uzaklık hesapla
        result_df = self._calculate_distance(result_df)
        
        return result_df
    
    def _calculate_tr(self, df):
        """True Range hesaplar"""
        high_low = df['high'] - df['low']
        high_close_prev = abs(df['high'] - df['close'].shift(1))
        low_close_prev = abs(df['low'] - df['close'].shift(1))
        
        tr = pd.DataFrame([high_low, high_close_prev, low_close_prev]).T.max(axis=1)
        return tr
    
    def _calculate_rma(self, series, period):
        """RMA (Running Moving Average) hesaplar"""
        alpha = 1.0 / period
        result = series.ewm(alpha=alpha, adjust=False).mean()
        return result
    
    def _calculate_supertrend(self, df):
        """SuperTrend değerlerini hesaplar"""
        # Uyarıları önlemek için kopyasını oluştur
        df = df.copy()
        
        df['up_band'] = df['source'] - (self.multiplier * df['atr'])
        df['down_band'] = df['source'] + (self.multiplier * df['atr'])
        
        # Trend hesaplama için hazırlık - boş sütunlar oluştur
        df['up_band_final'] = pd.Series(index=df.index, dtype=float)
        df['down_band_final'] = pd.Series(index=df.index, dtype=float)
        df['trend'] = pd.Series(index=df.index, dtype=float)
        
        # İlk değerler
        df.loc[df.index[0], 'trend'] = 1  # Başlangıçta yukarı trend
        df.loc[df.index[0], 'up_band_final'] = df.loc[df.index[0], 'up_band']
        df.loc[df.index[0], 'down_band_final'] = df.loc[df.index[0], 'down_band']
        
        # Ana SuperTrend hesaplama döngüsü
        for i in range(1, len(df)):
            curr_idx = df.index[i]
            prev_idx = df.index[i-1]
            
            # Üst bant hesaplaması
            if df.loc[prev_idx, 'close'] > df.loc[prev_idx, 'up_band_final']:
                df.loc[curr_idx, 'up_band_final'] = max(df.loc[curr_idx, 'up_band'], df.loc[prev_idx, 'up_band_final'])
            else:
                df.loc[curr_idx, 'up_band_final'] = df.loc[curr_idx, 'up_band']
                
            # Alt bant hesaplaması
            if df.loc[prev_idx, 'close'] < df.loc[prev_idx, 'down_band_final']:
                df.loc[curr_idx, 'down_band_final'] = min(df.loc[curr_idx, 'down_band'], df.loc[prev_idx, 'down_band_final'])
            else:
                df.loc[curr_idx, 'down_band_final'] = df.loc[curr_idx, 'down_band']
                
            # Trend hesapla
            if df.loc[curr_idx, 'close'] > df.loc[prev_idx, 'down_band_final']:
                df.loc[curr_idx, 'trend'] = 1  # up trend
            elif df.loc[curr_idx, 'close'] < df.loc[prev_idx, 'up_band_final']:
                df.loc[curr_idx, 'trend'] = -1  # down trend
            else:
                df.loc[curr_idx, 'trend'] = df.loc[prev_idx, 'trend']  # trend devam ediyor
        
        # SuperTrend değeri
        df['supertrend'] = np.where(df['trend'] == 1, df['up_band_final'], df['down_band_final'])
        
        # Sinyal hesapla
        df['signal'] = 0
        df.loc[(df['trend'] == 1) & (df['trend'].shift(1) == -1), 'signal'] = 1  # up sinyali
        df.loc[(df['trend'] == -1) & (df['trend'].shift(1) == 1), 'signal'] = -1  # down sinyali
        
        return df
    
    def _calculate_distance(self, df):
        """SuperTrend çizgisine olan uzaklığı hesaplar"""
        # Fiyat ile SuperTrend arasındaki mutlak uzaklık
        df['st_distance'] = abs(df['close'] - df['supertrend'])
        
        # Yüzde olarak uzaklık
        df['st_distance_pct'] = (df['st_distance'] / df['close']) * 100
        
        return df
    
    def get_trend_status(self, df):
        """
        Güncel trend durumu bilgilerini döndürür
        
        Dönüş:
        dict: Trend durumu bilgileri
        """
        if len(df) == 0:
            return {
                'trend': 'bilinmiyor',
                'signal': 'bilinmiyor',
                'supertrend': 0,
                'current_price': 0,
                'distance': 0,
                'distance_pct': 0,
                'formatted_supertrend': 'N/A',
                'formatted_price': 'N/A',
                'formatted_distance': 'N/A'
            }
        
        last_row = df.iloc[-1]
        
        trend_direction = 'up' if last_row['trend'] == 1 else 'down'
        signal = 'nötr'
        if last_row['signal'] == 1:
            signal = 'up'
        elif last_row['signal'] == -1:
            signal = 'down'
            
        # Değerleri al
        supertrend_value = last_row['supertrend']
        current_price = last_row['close']
        distance = last_row['st_distance']
        distance_pct = last_row['st_distance_pct']
        
        # Değerleri formatla
        formatted_supertrend = format_price_standard(supertrend_value)
        formatted_price = format_price_standard(current_price)
        formatted_distance = format_price_standard(distance)
        
        return {
            'trend': trend_direction,
            'signal': signal,
            'supertrend': supertrend_value,
            'current_price': current_price,
            'distance': distance,
            'distance_pct': distance_pct,
            'formatted_supertrend': formatted_supertrend,
            'formatted_price': formatted_price,
            'formatted_distance': formatted_distance
        } 