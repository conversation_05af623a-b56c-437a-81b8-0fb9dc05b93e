import os
import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Optional
from bybit_client import BybitClient
from utils import format_price_standard
from loguru import logger

# Logs klasörünü oluştur
os.makedirs("logs", exist_ok=True)

# SFP log dosya yolu tanımla
sfp_log_file_path = "logs/sfp_analysis.log"

# <PERSON><PERSON><PERSON> kullanıyoruz, ek yapılandırma gerekli değil

class SFPAnalyzer:
    """
    Swing Failure Pattern (SFP) analizi için sınıf.
    Belirtilen PineScript koduna (`emreSfp.txt`) dayanmaktadır.
    """

    def __init__(self, plen: int = 5, lookback: int = 12, is_opposite: bool = False, client: Optional['BybitClient'] = None):
        """
        SFPAnalyzer sınıfını başlatır.

        Args:
            plen (int): Pivot Yüksek/Düşük tespiti için kullanılacak periyot.
                        PineScript'teki ta.pivothigh(plen, 0) mantığına karşılık gelir.
                        Varsayılan: 10.
            lookback (int): SFP için geriye doğru bakılacak maksimum mum sayısı.
                            Varsayılan: 48.
            is_opposite (bool): SFP mumunun ters yönde olup olmadığını kontrol etme seçeneği.
                                True ise, Yüksek SFP için düşüş mumu, Düşük SFP için
                                yükseliş mumu gereklidir. Varsayılan: False.
            client (Optional[BybitClient]): Mevcut BybitClient instance'ı. None ise yeni oluşturulur.
        """
        self.plen = plen
        self.lookback = lookback
        self.is_opposite = is_opposite

        # Dependency Injection: Mevcut client varsa kullan, yoksa yeni oluştur
        if client is not None:
            self.client = client
            logger.info("[SFP Init] ✅ Mevcut BybitClient instance'ı kullanılıyor (Dependency Injection)")
        else:
            try:
                self.client = BybitClient()
                logger.warning("[SFP Init] ⚠️  Yeni BybitClient oluşturuldu (Dependency Injection önerilir)")
            except Exception as e:
                logger.error(f"[SFP Init] ❌ BybitClient başlatılamadı: {e}")
                self.client = None  # Hata durumunda None ata

        if self.plen <= 0:
            logger.warning(f"Geçersiz 'plen' değeri ({self.plen}). 1 olarak ayarlandı.")
            self.plen = 1
        if self.lookback <= 0:
            logger.warning(f"Geçersiz 'lookback' değeri ({self.lookback}). 1 olarak ayarlandı.")
            self.lookback = 1

        logger.info(f"SFPAnalyzer başlatıldı: plen={self.plen}, lookback={self.lookback}, is_opposite={self.is_opposite}")

    def _calculate_pivots(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Verilen DataFrame üzerinde ta.pivothigh(plen, 0) ve ta.pivotlow(plen, 0)
        mantığına göre pivot noktalarını hesaplar ve boolean serileri olarak ekler.

        Pivot Yüksek (is_ph): Mevcut mumun yüksekliği, önceki 'plen' mumun
                              yüksekliğinden KESİNLİKLE büyükse True.
        Pivot Düşük (is_pl): Mevcut mumun düşüklüğü, önceki 'plen' mumun
                             düşüklüğünden KESİNLİKLE düşükse True.

        Args:
            df (pd.DataFrame): OHLCV verilerini içeren DataFrame.

        Returns:
            pd.DataFrame: 'is_ph' ve 'is_pl' sütunları eklenmiş DataFrame.
        """
        df_copy = df.copy()
        plen = self.plen

        if len(df_copy) <= plen:
            logger.warning(f"Pivot hesaplaması için yetersiz veri ({len(df_copy)} mum, {plen} gerekli). Pivotlar hesaplanamayacak.")
            df_copy['is_ph'] = False
            df_copy['is_pl'] = False
            return df_copy

        logger.debug(f"Pivotlar hesaplanıyor (plen={plen})...")

        # Vektörel Hesaplama
        highs = df_copy['high']
        is_ph_series = pd.Series(True, index=df_copy.index)
        for i in range(1, plen + 1):
            is_ph_series = is_ph_series & (highs >= highs.shift(i))
        df_copy['is_ph'] = is_ph_series.fillna(False)

        lows = df_copy['low']
        is_pl_series = pd.Series(True, index=df_copy.index)
        for i in range(1, plen + 1):
            is_pl_series = is_pl_series & (lows <= lows.shift(i))
        df_copy['is_pl'] = is_pl_series.fillna(False) # Shift'ten kaynaklanan NaN'ları False yap

        ph_count = df_copy['is_ph'].sum()
        pl_count = df_copy['is_pl'].sum()
        logger.debug(f"Pivot hesaplaması tamamlandı. PH: {ph_count}, PL: {pl_count}")

        return df_copy

    def _detect_sfp_on_dataframe(self, df_with_pivots: pd.DataFrame) -> List[Dict]:
        """
        Pivotları hesaplanmış DataFrame üzerinde SFP tespitini gerçekleştirir.
        PineScript (`emreSfp.txt`) `f_sfp` fonksiyonunun mantığını uygular.

        Args:
            df_with_pivots (pd.DataFrame): '_calculate_pivots' tarafından döndürülen DataFrame.

        Returns:
            List[Dict]: Tespit edilen SFP'lerin listesi. Her sözlük şunları içerir:
                - 'sfp_bar_time': SFP'nin oluştuğu mumun zaman damgası.
                - 'sfp_type': 'Bearish' (Yüksek SFP) veya 'Bullish' (Düşük SFP).
                - 'sfp_price': SFP mumundaki ilgili fiyat (Bearish için high, Bullish için low).
                - 'failed_pivot_time': Başarısız olan önceki pivotun zaman damgası.
                - 'failed_pivot_price': Başarısız olan önceki pivotun fiyatı.
                - 'failed_pivot_type': 'High' veya 'Low'.
                - 'symbol': Analiz edilen sembol (analyze_symbol içinde eklenecek).
                - 'timeframe': Kullanılan zaman dilimi (analyze_symbol içinde eklenecek).
        """
        detected_sfps = []
        df = df_with_pivots
        n_rows = len(df)

        # Iterasyon için başlangıç indeksi (pivot ve lookback için yeterli geçmiş olmalı)
        # Pivotlar 'plen' kadar geçmişe bakar. Lookback en fazla 'lookback' kadar geriye gider.
        start_row_index = max(self.plen, self.lookback) + 1 # 0-bazlı index için

        if start_row_index >= n_rows:
            logger.debug("SFP tespiti için yeterli mum sayısı yok.")
            return []

        logger.debug(f"SFP tespiti için mumlar işleniyor (Başlangıç satırı: {start_row_index}, Toplam satır: {n_rows})...")

        
        # Sorunsuz
        for current_row in range(start_row_index, n_rows):
            current_series = df.iloc[current_row]
            current_time = current_series.get('timestamp') # Varsayılan

            # Mevcut mum verileri
            so = current_series['open']
            sh = current_series['high']
            sl = current_series['low']
            sc = current_series['close']
            is_current_ph = current_series['is_ph']
            is_current_pl = current_series['is_pl']

            # --- Yüksek SFP (Bearish) Kontrolü ---
            is_high_sfp = False
            failed_ph_time = None
            failed_ph_price = None

            if is_current_ph:
                hc2 = False
                # PineScript maxp = high[1] ile başlar
                # Bizim iloc[current_row - 1] ile almamız gerekir
                if current_row > 0:
                    maxp = df['high'].iloc[current_row - 1]
                else:
                    maxp = -np.inf # İlk bar için mantıklı bir başlangıç

                # Geriye doğru lookback döngüsü (i=1'den başlar)
                for i in range(1, self.lookback + 1):
                    past_row = current_row - i
                    if past_row < 0: break # DataFrame sınırları dışına çıkma

                    past_series = df.iloc[past_row]
                    past_time = past_series.get('timestamp')

                    co = past_series['open']
                    ch = past_series['high']
                    cl = past_series['low']
                    cc = past_series['close']
                    is_past_ph = past_series['is_ph']

                    # Kırılma koşulu: Geçmişteki high >= mevcut high
                    if ch >= sh:
                        break

                    # Ana SFP Koşulu
                    if ch < sh and ch > max(so, sc) and is_past_ph and ch > maxp:
                        hc2 = True
                        failed_ph_time = past_time
                        failed_ph_price = ch
                        logger.debug(f"[{current_time}] Bearish SFP adayı: Başarısız PH {format_price_standard(failed_ph_price)} ({failed_ph_time}) bulundu.")
                        break

                    # maxp güncellemesi
                    maxp = max(maxp, ch)

                is_high_sfp = is_current_ph and hc2

                # Ters mum yönü kontrolü
                if self.is_opposite and is_high_sfp and not (so > sc): # Ayı mumu değilse (open > close değilse)
                    is_high_sfp = False
                    if hc2: logger.debug(f"[{current_time}] Bearish SFP is_opposite filtresine takıldı (mum ayı değil).")


            # --- Düşük SFP (Bullish) Kontrolü ---
            is_low_sfp = False
            failed_pl_time = None
            failed_pl_price = None

            if is_current_pl:
                lc2 = False
                # PineScript minp = low[1] ile başlar
                if current_row > 0:
                     minp = df['low'].iloc[current_row - 1]
                else:
                     minp = np.inf

                # Geriye doğru lookback döngüsü (i=1'den başlar, Bearish SFP ile aynı)
                for i in range(1, self.lookback + 1):
                    past_row = current_row - i
                    if past_row < 0: break

                    past_series = df.iloc[past_row]
                    past_time = past_series.get('timestamp')

                    co = past_series['open']
                    ch = past_series['high']
                    cl = past_series['low']
                    cc = past_series['close']
                    is_past_pl = past_series['is_pl']

                    # Kırılma koşulu: Geçmişteki low <= mevcut low
                    if cl <= sl:
                        break

                    # Ana SFP Koşulu
                    if sl < cl and min(so, sc) > cl and is_past_pl and cl < minp:
                        lc2 = True
                        failed_pl_time = past_time
                        failed_pl_price = cl
                        logger.debug(f"[{current_time}] Bullish SFP adayı: Başarısız PL {format_price_standard(failed_pl_price)} ({failed_pl_time}) bulundu.")
                        break

                    # minp güncellemesi
                    minp = min(minp, cl)

                is_low_sfp = is_current_pl and lc2

                # Ters mum yönü kontrolü
                if self.is_opposite and is_low_sfp and not (sc > so): # Boğa mumu değilse (close > open değilse)
                    is_low_sfp = False
                    if lc2: logger.debug(f"[{current_time}] Bullish SFP is_opposite filtresine takıldı (mum boğa değil).")


            # --- Sonuçları Kaydet ---
            if is_high_sfp and failed_ph_time is not None:
                sfp_data = {
                    'sfp_bar_time': current_time,
                    'sfp_type': 'Bearish',
                    'sfp_price': sh, # Mevcut mumun high'ı
                    'failed_pivot_time': failed_ph_time,
                    'failed_pivot_price': failed_ph_price,
                    'failed_pivot_type': 'High'
                }
                detected_sfps.append(sfp_data)
                logger.debug(f"--> Bearish SFP Eklendi: {sfp_data}")

            elif is_low_sfp and failed_pl_time is not None:
                 sfp_data = {
                    'sfp_bar_time': current_time,
                    'sfp_type': 'Bullish',
                    'sfp_price': sl, # Mevcut mumun low'u
                    'failed_pivot_time': failed_pl_time,
                    'failed_pivot_price': failed_pl_price,
                    'failed_pivot_type': 'Low'
                 }
                 detected_sfps.append(sfp_data)
                 logger.debug(f"--> Bullish SFP Eklendi: {sfp_data}")

        logger.debug(f"SFP tespiti tamamlandı. Toplam {len(detected_sfps)} SFP bulundu.")
        return detected_sfps

    def analyze_symbol(self, symbol: str, timeframe: str) -> List[Dict]:
        """
        Belirli bir sembol ve zaman dilimi için Bybit'ten veri çeker,
        pivotları hesaplar ve SFP'leri tespit eder.
        """
        if self.client is None:
            logger.error(f"[{symbol}/{timeframe}] Bybit client başlatılmadığı için analiz yapılamıyor.")
            return []

        logger.debug(f"SFP analizi başlatılıyor: {symbol} {timeframe}")
        try:
            # Gerekli mum sayısı: lookback + plen + biraz tampon
            limit = self.lookback + self.plen + 10
            logger.debug(f"Veri çekiliyor: {symbol} {timeframe} (limit={limit})")
            candles = self.client.fetch_klines(symbol, timeframe, limit=limit)

            # Mum verisi kontrolü
            required_length = self.lookback + self.plen + 2  # Minimum gereken mum sayısı
            if candles is None or candles.empty:
                logger.warning(f"[{symbol}/{timeframe}] Veri çekilemedi veya boş.")
                return []
            if len(candles) < required_length:
                logger.warning(f"[{symbol}/{timeframe}] Yetersiz veri ({len(candles)} mum, en az {required_length} gerekli).")
                return []

            logger.debug(f"[{symbol}/{timeframe}] {len(candles)} mum verisi çekildi.")

            # Pivotları hesapla
            df_with_pivots = self._calculate_pivots(candles)

            # SFP'leri tespit et
            sfps = self._detect_sfp_on_dataframe(df_with_pivots)

            # Sonuçlara sembol ve zaman dilimini ekle
            for sfp in sfps:
                sfp['symbol'] = symbol
                sfp['timeframe'] = timeframe

            count = len(sfps)
            if count > 0:
                logger.info(f"SFP SONUÇ | {symbol} {timeframe} | {count} SFP bulundu.")
            else:
                logger.debug(f"SFP SONUÇ | {symbol} {timeframe} | SFP bulunamadı.")

            return sfps

        except Exception as e:
            logger.error(f"SFP analizi sırasında hata ({symbol} {timeframe}): {e}", exc_info=True)
            return []

    def analyze_all_symbols(self) -> List[Dict]:
        """
        Çevre değişkenlerinde tanımlı tüm semboller ve belirli zaman dilimleri
        için SFP analizini çalıştırır, sonuçları loglar ve listeyi döndürür.

        Returns:
            List[Dict]: Analiz edilen tüm semboller/zaman dilimleri için
                        tespit edilen SFP'lerin listesi.
        """
        logger.info("===== TÜM SEMBOLLER İÇİN SFP ANALİZİ BAŞLADI =====")
        symbols = os.getenv("SYMBOLS", "BTCUSDT,ETHUSDT,SOLUSDT").split(",")
        timeframes = os.getenv("TIMEFRAMES", "60,240,D").split(",")

        all_sfps_found_list: List[Dict] = []

        for symbol in symbols:
            for timeframe in timeframes:
                symbol_sfps = self.analyze_symbol(symbol, timeframe)
                all_sfps_found_list.extend(symbol_sfps)

        # --- Özet Raporlama (Konsol - Daha Basit) ---
        logger.info("---------- SFP Analiz Özeti (Detaylar log dosyasında) ----------")
        total_sfps_found = len(all_sfps_found_list)
        if total_sfps_found == 0:
            logger.info(">> Analiz edilen periyotta SFP bulunamadı.")
        else:
            bullish_count = sum(1 for sfp in all_sfps_found_list if sfp['sfp_type'] == 'Bullish')
            bearish_count = total_sfps_found - bullish_count
            logger.info(f"Toplam {total_sfps_found} SFP bulundu (Bullish: {bullish_count}, Bearish: {bearish_count}).")
        logger.info("-------------------------------------------------------------")


        # Dosyaya yazmak için özet (Detaylı Format)
        try:
            # Düzeltme: Tanımlanan global değişkeni kullan
            # 'a' modu yerine 'w' modu ile dosyayı her seferinde yeniden yazalım mı?
            # Yoksa 'a' ile eklemeye devam mı edelim? Şimdilik eklemeye devam edelim.
            with open(sfp_log_file_path, 'a', encoding='utf-8') as f:
                f.write(f"\n---------- SFP ÖZET RAPORU ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ----------\n") # Zaman damgası ekleyelim
                if not all_sfps_found_list:
                    f.write(">> Analiz edilen periyotta SFP bulunamadı.\n")
                else:
                     sorted_sfps = sorted(all_sfps_found_list, key=lambda x: x['sfp_bar_time'], reverse=True)
                     f.write(f"Toplam {len(sorted_sfps)} SFP bulundu:\n")
                     for sfp in sorted_sfps:
                         tf_label = f"{sfp['timeframe']}m" if sfp['timeframe'].isdigit() else sfp['timeframe']
                         # failed_pivot_time null olabilir, kontrol ekleyelim
                         failed_pivot_time_val = sfp.get('failed_pivot_time')
                         sfp_bar_time_val = sfp.get('sfp_bar_time')
                         failed_pivot_price_val = sfp.get('failed_pivot_price')

                         failed_pivot_time_str = failed_pivot_time_val.strftime('%Y-%m-%d %H:%M') if failed_pivot_time_val is not None and pd.notna(failed_pivot_time_val) else 'N/A'
                         sfp_bar_time_str = sfp_bar_time_val.strftime('%Y-%m-%d %H:%M') if sfp_bar_time_val is not None and pd.notna(sfp_bar_time_val) else 'N/A'
                         failed_pivot_price_str = format_price_standard(failed_pivot_price_val) if failed_pivot_price_val is not None and pd.notna(failed_pivot_price_val) else 'N/A'

                         f.write(
                             f"- {sfp.get('symbol','N/A')} / {tf_label} / {sfp.get('sfp_type','N/A')} / "
                             f"SFP Bar: {sfp_bar_time_str} / "
                             f"Failed Pivot: {failed_pivot_price_str} at {failed_pivot_time_str}\n"
                         )
                f.write("-------------------------------------------------\n\n") # Raporlar arasına boşluk
        except Exception as e:
            logger.error(f"SFP özet raporu dosyaya yazılamadı: {e}") # Hata mesajı zaten loglanıyor

        logger.info("===== TÜM SEMBOLLER İÇİN SFP ANALİZİ TAMAMLANDI =====")
        # Değişiklik: Tespit edilen SFP listesini döndür
        return all_sfps_found_list


# Doğrudan çalıştırma ve test için
if __name__ == "__main__":
    # .env dosyasını yüklemek için dotenv importu ekleyelim
    from dotenv import load_dotenv
    load_dotenv()
    logger.info("SFPAnalyzer doğrudan çalıştırılıyor...")

    # Test için farklı parametreler deneyebilirsiniz:
    # analyzer = SFPAnalyzer(plen=10, lookback=50, is_opposite=True)
    analyzer = SFPAnalyzer(plen=21, lookback=489, is_opposite=False)

    analyzer.analyze_all_symbols()
    logger.info("Doğrudan çalıştırma tamamlandı.")