"""
İstatistik Raporlama Modülü (StatsReporter)
Sistemdeki işlem istatistiklerini takip eder ve düzenli raporlar oluşturur.
"""

import os
import time
import threading
import schedule
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
from loguru import logger
from stats_tracker import StatsTracker
from telegram_notifier import TelegramNotifier
from utils import format_price_standard

class StatsReporter:
    """
    İstatistik raporlama sınıfı. İşlem sonuçlarını ve performans metriklerini
    düzenli olarak raporlar.
    """

    def __init__(self, stats_tracker: StatsTracker, telegram_notifier: TelegramNotifier):
        """
        StatsReporter sınıfının başlatıcısı.
        
        Args:
            stats_tracker: İstatistik takip nesnesi
            telegram_notifier: Telegram bildirim nesnesi
        """
        self.stats_tracker = stats_tracker
        self.telegram = telegram_notifier
        self.is_running = False
        self.report_thread = None

        logger.info("StatsReporter başlatıldı.")

    def generate_summary_report(self) -> str:
        """Özet performans raporu oluşturur."""
        try:
            metrics = self.stats_tracker.get_performance_metrics()

            report = "📊 *İŞLEM İSTATİSTİKLERİ RAPORU* 📊\n\n"

            # Temel metrikler
            report += f"📈 *Toplam Sinyal:* `{metrics['total_signals']}`\n"
            report += f"🔄 *Tamamlanan İşlem:* `{metrics['completed_trades']}`\n"
            report += f"✅ *Başarılı İşlem:* `{metrics['successful_trades']}`\n"
            report += f"❌ *Başarısız İşlem:* `{metrics['failed_trades']}`\n"
            report += f"📊 *Başarı Oranı:* `%{metrics['success_rate']:.1f}`\n"
            report += f"💰 *Ortalama Kar:* `%{metrics['avg_profit_percentage']:.2f}`\n"
            report += f"💵 *Toplam Kar:* `%{metrics['total_profit_percentage']:.2f}`\n\n"

            # En iyi işlem
            if metrics.get('best_trade'):
                best_trade_data = metrics['best_trade']
                profit_pct = best_trade_data.get('profit_percentage', 0)
                
                # Kâr/Zarar durumuna göre etiketi ve emojiyi dinamik olarak belirle
                label = "kar" if profit_pct >= 0 else "zarar"
                emoji = "🏆" if profit_pct >= 0 else "📉"
                
                # DÜZELTME: Sembolü al, eğer yoksa signal_id'den ayıkla
                symbol = best_trade_data.get('symbol') or best_trade_data.get('signal_id', 'N/A').split('_')[0]
                
                report += f"{emoji} *En İyi İşlem:*\n"
                report += f"  └─ {symbol} | `%{profit_pct:.2f}` {label}\n\n"

            # En kötü işlem
            if metrics.get('worst_trade'):
                worst_trade_data = metrics['worst_trade']
                profit_pct = worst_trade_data.get('profit_percentage', 0)

                # Sadece negatif ise göster (bu mantık korunabilir)
                if profit_pct < 0:
                    # DÜZELTME: Sembolü al, eğer yoksa signal_id'den ayıkla
                    symbol = worst_trade_data.get('symbol') or worst_trade_data.get('signal_id', 'N/A').split('_')[0]
                    
                    report += f"📉 *En Kötü İşlem:*\n"
                    report += f"  └─ {symbol} | `%{profit_pct:.2f}` zarar\n\n"

            # En iyi sembol
            if metrics.get('best_symbol'):
                best_sym = metrics['best_symbol']
                report += f"💎 *En İyi Sembol:*\n"
                report += f"  └─ {best_sym['symbol']} | `%{best_sym['success_rate']:.1f}` başarı oranı\n"
                report += f"  └─ Ort. Kar: `%{best_sym['avg_profit']:.2f}` ({best_sym['total_trades']} işlem)\n\n"

            # Aktif işlemler
            active_signals_count = self.stats_tracker.get_active_signals_count()
            report += f"🔔 *Aktif İşlem Sayısı:* `{active_signals_count}`\n\n"

            # Son güncelleme zamanı
            last_updated_iso = metrics.get('last_updated')
            if last_updated_iso:
                last_updated = datetime.fromisoformat(last_updated_iso)
                report += f"🕒 *Son Güncelleme:* `{last_updated.strftime('%Y-%m-%d %H:%M:%S')}`"

            return report

        except Exception as e:
            logger.error(f"Özet rapor oluşturulurken hata: {e}", exc_info=True)
            return f"❌ *Rapor Hatası:* `{str(e)}`"

    def generate_recent_trades_report(self) -> str:
        """Son işlemlerin raporunu oluşturur."""
        try:
            results_file = self.stats_tracker.results_file
            if not os.path.exists(results_file) or os.path.getsize(results_file) == 0:
                return "📝 *SON İŞLEMLER*\n\nHenüz tamamlanmış işlem bulunmuyor."

            df = pd.read_csv(results_file)
            if df.empty:
                return "📝 *SON İŞLEMLER*\n\nHenüz tamamlanmış işlem bulunmuyor."

            df['result_time'] = pd.to_datetime(df['result_time'])
            df = df.sort_values('result_time', ascending=False).head(5)

            report = "📝 *SON İŞLEMLER*\n\n"

            for idx, row in df.iterrows():
                symbol = row['symbol']
                direction = "📈 LONG" if row['direction'] == "BULL" else "📉 SHORT"
                profit = row.get('profit_percentage', 0.0)
                status = "✅ KAZANÇ" if profit > 0 else "❌ KAYIP"
                result_time = pd.to_datetime(row['result_time']).strftime('%d/%m/%Y %H:%M')

                report += f"*{symbol}* | {direction} | {status}\n"
                report += f"└─ Kar/Zarar: `%{profit:.2f}`\n"
                report += f"└─ Tarih: `{result_time}`\n\n"

            report += f"🔍 *Toplam:* `{len(df)}` işlem gösteriliyor."
            
            return report

        except Exception as e:
            logger.error(f"Son işlemler raporu oluşturulurken hata: {e}")
            return f"❌ *Rapor Hatası:* `{str(e)}`"

    def generate_active_signals_report(self) -> str:
        """
        StatsTracker'dan aktif sinyalleri alıp formatlanmış bir rapor oluşturur.
        Tekrar eden sembolleri filtreler, her sembolden sadece en sonuncuyu gösterir.
        """
        try:
            active_signals = self.stats_tracker.get_active_signals()
            if not active_signals:
                return "🔔 *AKTİF SİNYALLER*\n\n_Şu anda takip edilen aktif bir sinyal bulunmuyor._"

            # Sinyalleri benzersiz hale getir: her sembol için sadece en son sinyali tut
            unique_signals: Dict[str, Dict[str, Any]] = {}
            for signal in active_signals:
                symbol = signal.get("symbol")
                if symbol:
                    # Eğer sembol zaten varsa ve yeni sinyalin zamanı daha eskiyse atla
                    if symbol in unique_signals and unique_signals[symbol].get("entry_time", "") > signal.get("entry_time", ""):
                        continue
                    unique_signals[symbol] = signal
            
            processed_signals = list(unique_signals.values())

            report = "🔔 *AKTİF SİNYALLER*\n\n"
            report += f"_{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} itibarıyla {len(processed_signals)} adet sinyal takip ediliyor:_\n"
            report += "--------------------------------------\n"
            
            for signal in processed_signals:
                symbol = signal.get("symbol", "N/A")
                direction = signal.get("direction", "N/A")
                # BEAR yerine SHORT, BULL yerine LONG kullan
                direction_text = "SHORT" if direction == "BEAR" else "LONG" if direction == "BULL" else direction
                direction_emoji = "🔼" if direction == "BULL" else "🔻" if direction == "BEAR" else "❓"
                entry_price = signal.get("entry_price", 0)
                entry_time_iso = signal.get("entry_time")
                score = signal.get("score", 0.0)

                try:
                    entry_time = datetime.fromisoformat(entry_time_iso).strftime('%d-%m %H:%M')
                except (ValueError, TypeError):
                    entry_time = "N/A"

                report += f"{direction_emoji} *{symbol}* - {direction_text}\n"
                report += f"  ├─ Giriş: `{format_price_standard(entry_price)}`\n"
                report += f"  ├─ Skor: `{score:.1f}`\n"
                report += f"  └─ Zaman: `{entry_time}`\n\n"

            return report

        except Exception as e:
            logger.error(f"Aktif sinyaller raporu oluşturulurken hata: {e}", exc_info=True)
            return "❌ *Aktif Sinyaller Raporu Hatası*"

    def send_scheduled_report(self):
        """Zamanlanmış (6 saatlik) ana raporu oluşturur ve gönderir."""
        try:
            logger.info("Zamanlanmış (6 saatlik) istatistik raporu oluşturuluyor...")

            # Özet ve son işlemler raporları
            summary_report = self.generate_summary_report()
            self.telegram.send_message(summary_report)
            recent_trades = self.generate_recent_trades_report()
            self.telegram.send_message(recent_trades)

            # Aktif sinyaller raporunu gönder
            active_signals_report = self.generate_active_signals_report()
            self.telegram.send_message(active_signals_report)
            logger.info("6 saatlik periyodik raporlar gönderildi.")

        except Exception as e:
            logger.error(f"Zamanlanmış rapor gönderilirken hata: {e}", exc_info=True)
            self.telegram.send_message(f"❌ *İstatistik Raporu Hatası:* `{str(e)}`")

    def send_hourly_active_signals_report(self):
        """Saatlik aktif sinyaller raporunu, SADECE aktif sinyal varsa gönderir."""
        try:
            if not self.stats_tracker.get_active_signals():
                logger.info("Saatlik aktif sinyal kontrolü: Takip edilen sinyal bulunmadığı için rapor gönderilmedi.")
                return

            logger.info("Saatlik aktif sinyaller raporu gönderiliyor (sinyal var)...")
            report = self.generate_active_signals_report()
            self.telegram.send_message(report)
            logger.info("Aktif sinyaller raporu Telegram'a gönderildi.")
        except Exception as e:
            logger.error(f"Saatlik rapor gönderilirken hata: {e}", exc_info=True)
            self.telegram.send_message(f"❌ *Saatlik Rapor Hatası:* `{str(e)}`")

    def send_active_signals_report(self):
        """Aktif işlem sinyallerini Telegram'a gönderir."""
        try:
            report = self.generate_active_signals_report()
            if report:
                self.telegram.send_message(report)
                logger.info(f"Aktif işlem sinyalleri raporu Telegram'a gönderildi.")
            return True
        except Exception as e:
            logger.error(f"Aktif işlem sinyalleri raporu gönderilirken hata: {e}")
            return False

    def schedule_reporter(self):
        """Zamanlayıcıyı başlatır ve raporları zamanlar."""
        if self.is_running:
            logger.warning("Zamanlayıcı zaten çalışıyor!")
            return

        def run_scheduler():
            try:
                # İlk raporu hemen gönder
                self.send_scheduled_report()                # Zamanlayıcıları ayarla
                schedule.clear()
                schedule.every().day.at("00:00").do(self.send_scheduled_report)
                schedule.every().day.at("06:00").do(self.send_scheduled_report)  # 6 saatlik aralık için eklendi
                schedule.every().day.at("12:00").do(self.send_scheduled_report)
                schedule.every().day.at("18:00").do(self.send_scheduled_report)  # 6 saatlik aralık için eklendi
                # schedule.every().hour.at(":01").do(self.send_hourly_active_signals_report)  # Devre dışı - artık değişiklik bazlı çalışıyor

                self.is_running = True
                logger.info("İstatistik raporlayıcı başlatıldı - 6 saatlik özet raporları gönderilecek. Aktif sinyaller değişiklik bazlı bildirilecek.")

                while self.is_running:
                    try:
                        schedule.run_pending()
                        time.sleep(1)
                    except Exception as e:
                        logger.error(f"Zamanlayıcı döngüsünde hata: {e}")
                        time.sleep(5)

            except Exception as e:
                logger.error(f"Zamanlayıcı thread'inde kritik hata: {e}")
                self.is_running = False

        self.stop_reporter()
        self.report_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.report_thread.start()

    def stop_reporter(self):
        """Zamanlayıcıyı güvenli şekilde durdurur."""
        if self.is_running:
            logger.info("Zamanlayıcı durduruluyor...")
            self.is_running = False
            
            if self.report_thread and self.report_thread.is_alive():
                try:
                    self.report_thread.join(timeout=5)
                    if self.report_thread.is_alive():
                        logger.warning("Zamanlayıcı thread'i zorla kapatıldı")
                except Exception as e:
                    logger.error(f"Thread kapatılırken hata: {e}")
            
            schedule.clear()
            logger.info("İstatistik raporlayıcı durduruldu.")

# Test için
if __name__ == "__main__":
    import os
    from dotenv import load_dotenv
    
    # .env dosyasından çevresel değişkenleri yükle
    load_dotenv()
    
    # StatsTracker oluştur
    stats_tracker = StatsTracker()
    
    # TelegramNotifier oluştur
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    telegram = TelegramNotifier(bot_token, chat_id)
    
    # StatsReporter oluştur
    reporter = StatsReporter(stats_tracker, telegram)
    
    # Test raporu oluştur (ama gönderme)
    test_report = reporter.generate_summary_report()
    print("\n--- Özet Rapor (sadece konsola yazdırıldı) ---\n")
    print(test_report)
    
    # Zamanlayıcıyı başlat (artık başlangıçta rapor göndermeyecek)
    reporter.schedule_reporter()
    print("\nZamanlayıcı başlatıldı. 6 saatte bir rapor gönderilecek.")
    
    # Programın kapanmaması için bekle
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        reporter.stop_reporter()
        print("Program durduruldu.")
