import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Any
from loguru import logger

class FVGAnalyzer:
    """
    FVG (Fair Value Gap) Analizörü.
    Verilen mum verilerinde FVG'leri tespit eder ve EQ bölgelerini hesaplar.
    """

    def find_fvgs(self, candles: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Verilen mum verilerinde FVG'leri bulur ve EQ bölgelerini hesaplar.

        Args:
            candles: Mum verilerini içeren Pandas DataFrame ('high', 'low' sütunları gerekli).
                     DataFrame'in zaman sırasına göre (eskiden yeniye) sıralı olduğu varsayılır.

        Returns:
            Tespit edilen FVG'lerin listesi. Her FVG bir sözlüktür:
            {
                'type': 'bullish' veya 'bearish',
                'top': FVG'nin üst fiyatı,
                'bottom': FVG'nin alt fiyatı,
                'eq': FVG'nin eşit bölgesi (orta noktası),
                'timestamp': FVG'nin oluştuğu orta mumun zaman damgası,
                'filled': FVG'nin doldurulup doldurulmadığı (default: False)
            }
        """
        fvgs = []
        if candles is None or len(candles) < 3:
            logger.warning("FVG analizi için yetersiz mum verisi (< 3).")
            return fvgs

        # Mumlar üzerinde 3'lü gruplar halinde gezin (i-2, i-1, i)
        for i in range(2, len(candles)):
            prev_prev_candle = candles.iloc[i - 2]
            prev_candle = candles.iloc[i - 1]
            current_candle = candles.iloc[i]

            # Bullish FVG Kontrolü: Önceki 2. mumun düşüğü > şimdiki mumun yükseği
            if prev_prev_candle['low'] > current_candle['high']:
                fvg_top = prev_prev_candle['low']
                fvg_bottom = current_candle['high']
                fvg_eq = (fvg_top + fvg_bottom) / 2  # EQ bölgesi hesaplama
                
                fvgs.append({
                    'type': 'bullish',
                    'top': fvg_top,
                    'bottom': fvg_bottom,
                    'eq': fvg_eq,
                    'timestamp': prev_candle['timestamp'],  # Ortadaki mumun zamanı
                    'filled': False  # Varsayılan olarak doldurulmamış
                })
                logger.debug(f"Bullish FVG bulundu @ {prev_candle['timestamp']}: Top={fvg_top}, Bottom={fvg_bottom}, EQ={fvg_eq}")

            # Bearish FVG Kontrolü: Önceki 2. mumun yükseği < şimdiki mumun düşüğü
            elif prev_prev_candle['high'] < current_candle['low']:
                fvg_top = current_candle['low']
                fvg_bottom = prev_prev_candle['high']
                fvg_eq = (fvg_top + fvg_bottom) / 2  # EQ bölgesi hesaplama
                
                fvgs.append({
                    'type': 'bearish',
                    'top': fvg_top,
                    'bottom': fvg_bottom,
                    'eq': fvg_eq,
                    'timestamp': prev_candle['timestamp'],  # Ortadaki mumun zamanı
                    'filled': False  # Varsayılan olarak doldurulmamış
                })
                logger.debug(f"Bearish FVG bulundu @ {prev_candle['timestamp']}: Top={fvg_top}, Bottom={fvg_bottom}, EQ={fvg_eq}")

        # FVG'lerin doldurulup doldurulmadığını kontrol et
        self._check_fvg_filled(fvgs, candles)
        
        logger.info(f"Toplam {len(fvgs)} FVG bulundu.")
        return fvgs
    
    def _check_fvg_filled(self, fvgs: List[Dict[str, Any]], candles: pd.DataFrame) -> None:
        """
        FVG'lerin sonraki mumlar tarafından doldurulup doldurulmadığını kontrol eder.
        
        Args:
            fvgs: FVG listesi
            candles: Mum verileri
        """
        if not fvgs or candles is None or candles.empty:
            return
            
        last_price = candles.iloc[-1]['close']
        
        for fvg in fvgs:
            fvg_timestamp = fvg['timestamp']
            fvg_type = fvg['type']
            fvg_top = fvg['top']
            fvg_bottom = fvg['bottom']
            
            # FVG'den sonraki mumları filtrele
            future_candles = candles[candles.index > candles[candles['timestamp'] == fvg_timestamp].index[0]]
            
            # FVG'nin doldurulup doldurulmadığını kontrol et
            if fvg_type == 'bullish':
                # Bullish FVG için, herhangi bir mumun düşüğü FVG'nin üstüne ulaştıysa doldurulmuş demektir
                filled = any(future_candles['low'] <= fvg_top)
            else:  # bearish
                # Bearish FVG için, herhangi bir mumun yükseği FVG'nin altına ulaştıysa doldurulmuş demektir
                filled = any(future_candles['high'] >= fvg_bottom)
                
            fvg['filled'] = filled
