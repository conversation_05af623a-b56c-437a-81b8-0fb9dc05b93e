from typing import Optional, List
from loguru import logger

def format_price_standard(price: Optional[float]) -> str:
    """
    Farklı büyüklükteki kripto para fiyatlarını standart formatta biçimlendirir.
    
    Args:
        price: Biçimlendirilecek fiyat değeri
        
    Returns:
        str: Biçimlendirilmiş fiyat değeri
    """
    if price is None:
        return "N/A"
    
    # Farklı hassasiyet seviyeleri için format belirleme
    if price >= 1000:
        return f"{price:.2f}"  # Örn: 45000.00
    elif price >= 100:
        return f"{price:.2f}"  # Örn: 500.00
    elif price >= 10:
        return f"{price:.2f}"  # Örn: 50.00
    elif price >= 1:
        return f"{price:.3f}"  # Örn: 5.000
    elif price >= 0.1:
        return f"{price:.4f}"  # Örn: 0.5000
    elif price >= 0.01:
        return f"{price:.5f}"  # Örn: 0.05000
    elif price >= 0.001:
        return f"{price:.6f}"  # Örn: 0.005000
    else:
        return f"{price:.8f}"  # Örn: 0.00050000 

def format_volume(volume: float) -> str:
    """
    Büyük sayısal hacim veya ciro değerlerini okunaklı bir formata ('B' Milyar, 'M' Milyon) çevirir.

    Args:
        volume (float): Formatlanacak sayısal değer.

    Returns:
        str: Formatlanmış string (örn. "1.23B", "45.67M", "890.12").
    """
    if volume >= 1_000_000_000:
        return f"{volume/1_000_000_000:.2f}B"
    elif volume >= 1_000_000:
        return f"{volume/1_000_000:.2f}M"
    else:
        return f"{volume:.2f}" # Milyon altına düşenler için direkt format

def shorten_indicator_names(indicators: List[str]) -> str:
    """
    Verilen gösterge adları listesini kısaltarak tek bir string haline getirir.
    Önceden tanımlanmış kısaltmalar kullanılır, yoksa baş harf alınır.
    (örn. ["MACD", "RSI"] -> "MR")
    
    Args:
        indicators (List[str]): Gösterge adlarının listesi.
        
    Returns:
        str: Kısaltılmış ve birleştirilmiş gösterge adları string'i.
    """
    indicator_map = {
        "MACD": "M",
        "MACD Histogram": "H", # Histogram için ayrı
        "RSI": "R",
        "Stochastic": "S",
        "CCI": "C",
        "Momentum": "M", # MACD ile aynı harf, dikkat
        "OBV": "O",
        "vwMACD": "V",
        "CMF": "C", # CCI ile aynı harf, dikkat
        "MFI": "F"
    }
    short_names = []
    for ind in indicators:
        # Eşleşme varsa haritasını, yoksa ilk harfini al (büyük harf)
        short_names.append(indicator_map.get(ind, ind[0].upper() if ind else ''))
    return "".join(short_names) 